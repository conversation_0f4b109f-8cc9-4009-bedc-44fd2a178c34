package main

import (
	"context"
	"fmt"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/config"
)

func main() {
	fmt.Println("Starting app...")
	ctx := context.Background()
	viperConfig := config.NewViper()
	logger := config.NewLogger(ctx, viperConfig)
	validator := config.NewValidator(viperConfig)
	pgdb := config.NewPostgreSQL(ctx, viperConfig)
	appHttp := config.NewHttp(viperConfig)

	config.Bootstrap(&config.BootstrapConfig{
		HTTP:     appHttp,
		Log:      logger,
		Validate: validator,
		Config:   viperConfig,
		PgDB:     pgdb,
	})

	apiPort := viperConfig.GetInt("api.port")

	server := http.Server{
		Addr:    ":" + fmt.Sprintf("%d", apiPort),
		Handler: appHttp,
	}

	fmt.Println("Server started on port", apiPort)
	err := server.ListenAndServe()
	if err != nil {
		panic(err)
	}
}
