package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

func NewViper() *viper.Viper {
	config := viper.New()
	config.SetConfigType("yaml")
	config.AutomaticEnv()

	// Set environment variable key mappings
	config.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		panic("failed to get current file path")
	}

	configPath := filepath.Join(filepath.Dir(filename), "config.yaml")
	config.AddConfigPath(filepath.Dir(configPath))
	config.SetConfigName("config")

	err := config.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %w", err))
	}

	// Try to read .env file only if it exists
	if envFile, err := godotenv.Read(".env"); err == nil {
		// Only set from .env if the value exists and is not empty
		if postgresURL := envFile["POSTGRESQL_URL"]; postgresURL != "" {
			config.Set("database.postgresql.url", postgresURL)
		}
		if webUrl := envFile["WEB_CLIENT_URL"]; webUrl != "" {
			config.Set("web_client.url", webUrl)
		}
	}

	// Override with environment variables if they exist
	// This ensures environment variables take precedence over .env file
	if postgresURL := os.Getenv("POSTGRESQL_URL"); postgresURL != "" {
		config.Set("database.postgresql.url", postgresURL)
	}

	if webUrl := os.Getenv("WEB_CLIENT_URL"); webUrl != "" {
		config.Set("web_client.url", webUrl)
	}

	return config
}
