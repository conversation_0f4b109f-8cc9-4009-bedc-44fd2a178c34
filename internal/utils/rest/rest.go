package rest

import (
	"encoding/json"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

type RespErr struct {
	Message string        `json:"message"`
	Details any           `json:"details"`
	Code    utils.ErrCode `json:"code"`
}

const (
	XCorrelationID = "X-Correlation-Id"
	DevTenant      = "dev"
)

func DecodeBody[T any](w http.ResponseWriter, r *http.Request) (*T, error) {
	var data T
	if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
		return nil, err
	}
	return &data, nil
}

func ErrorResponse(
	w http.ResponseWriter,
	r *http.Request,
	err utils.AppErr,
	statusCode int,
) {
	errorResp := RespErr{
		Message: err.Message,
		Details: err.Details,
		Code:    err.Code,
	}

	w.Header().Set(XCorrelationID, r.Context().Value(utils.CorrelationIDKey).(string))
	writeJSON(w, errorResp, statusCode)
}

func SuccessDResponse(w http.ResponseWriter, r *http.Request, data any, statusCode int) {
	writeJSON(w, data, statusCode)
}

func SuccessResponse(w http.ResponseWriter, r *http.Request, statusCode int) {
	w.WriteHeader(statusCode)
}

func writeJSON(w http.ResponseWriter, result any, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(result)
}
