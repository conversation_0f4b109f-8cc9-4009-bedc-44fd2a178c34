package rest

import (
	"errors"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

type RespErrHandlers map[utils.ErrCode]int

var BaseErrHandlers = RespErrHandlers{
	utils.InternalErrorCode: http.StatusInternalServerError,
	utils.MultiErrorCode:    http.StatusInternalServerError,
	utils.ParseErrorCode:    http.StatusBadRequest,
	utils.BadRequestCode:    http.StatusBadRequest,
	utils.UnauthorizedCode:  http.StatusUnauthorized,
	utils.NotFoundCode:      http.StatusNotFound,
	utils.ConflictCode:      http.StatusConflict,
}

func RespErrHandler(w http.ResponseWriter, r *http.Request, err error, message string, errHandlers ...RespErrHandlers) {
	var appErr utils.AppErr
	if errors.As(err, &appErr) {
		errCode := appErr.Code
		merged := mergeErrHandlers(append(errHandlers, BaseErrHandlers)...)
		if handler, exists := merged[errCode]; exists {
			ErrorResponse(w, r, appErr, handler)
		}
	}
}

func mergeErrHandlers(amps ...RespErrHandlers) RespErrHandlers {
	merged := RespErrHandlers{}
	for _, amp := range amps {
		for code, status := range amp {
			merged[code] = status
		}
	}
	return merged
}
