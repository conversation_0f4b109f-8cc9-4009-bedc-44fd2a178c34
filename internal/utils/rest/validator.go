package rest

import (
	"fmt"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/go-playground/validator/v10"
)

func DecodeAndValidate[T any](w http.ResponseWriter, r *http.Request, validate *validator.Validate) (*T, error) {
	body, err := DecodeBody[T](w, r)

	if err != nil {
		ErrorResponse(w, r,
			utils.ParseErrorf("failed to parse JSON", err, nil), http.StatusBadRequest)
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	err = validate.Struct(body)
	if err != nil {
		ErrorResponse(w, r,
			utils.ParseErrorf("failed to validate values", err, nil),
			http.StatusBadRequest)
		return nil, fmt.Errorf("failed to validate values: %w", err)
	}

	return body, nil
}
