package utils

import (
	"time"

	"aidanwoods.dev/go-paseto"
)

const TokenKey = "fhyona-token"

var (
	secretKey paseto.V4AsymmetricSecretKey
	publicKey paseto.V4AsymmetricPublicKey
)

func Init() {
	secretKey = paseto.NewV4AsymmetricSecretKey()
	publicKey = secretKey.Public()
}

func GetUserIdFromToken(token paseto.Token) (string, error) {
	id, err := token.GetString("user-id")
	if err != nil {
		return "", err
	}

	return id, nil
}

func GenerateToken(id string) (string, error) {
	token := paseto.NewToken()

	token.SetIssuedAt(time.Now())
	token.SetNotBefore(time.Now())
	token.SetExpiration(time.Now().Add(24 * time.Hour))

	token.SetString("user-id", id)

	signed := token.V4Sign(secretKey, nil)

	return signed, nil
}

func VerifyToken(signed string) (*paseto.Token, error) {
	parser := paseto.NewParser()

	parsedToken, err := parser.ParseV4Public(publicKey, signed, nil)

	if err != nil {
		return nil, err
	}

	return parsedToken, nil
}
