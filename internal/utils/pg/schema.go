package pg

import (
	"context"
	"errors"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func ExecuteInSchema(ctx context.Context, pool *pgxpool.Pool, fn func(context.Context, *pgxpool.Conn) error) error {
	conn, err := pool.Acquire(ctx)
	if err != nil {
		return err
	}
	defer conn.Release()

	tenantID, ok := ctx.Value(utils.TenantIDKey).(string)
	if !ok {
		return errors.New("tenant ID not found in context")
	}

	_, err = conn.Exec(ctx, fmt.Sprintf("SET search_path TO %s",
		pgx.Identifier{tenantID}.Sanitize()))
	if err != nil {
		return err
	}

	return fn(ctx, conn)
}
