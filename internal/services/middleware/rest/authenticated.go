package rest

import (
	"context"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Authenticated implements HTTPMiddleware.
func (h *httpMiddleware) Authenticated(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		cookie, err := r.<PERSON>(utils.TokenKey)
		if err != nil {
			utils.LogErr(r.Context(), h.Log, err)
			rest.ErrorResponse(w, r, utils.Unauthorizedf("cookie not found", err, nil), http.StatusUnauthorized)
			return
		}

		token := cookie.Value

		verifiedToken, err := utils.VerifyToken(token)
		if err != nil {
			utils.LogErr(r.Context(), h.Log, err)
			rest.ErrorResponse(w, r, utils.Unauthorizedf("invalid or expired token", err, nil), http.StatusUnauthorized)
			return
		}

		userId, err := utils.GetUserIdFromToken(*verifiedToken)
		if err != nil {
			utils.LogErr(r.Context(), h.Log, err)
			rest.ErrorResponse(w, r, utils.Unauthorizedf("user id not found in token", err, nil), http.StatusUnauthorized)
			return
		}

		ctx := context.WithValue(r.Context(), utils.UserIDKey, userId)
		next(w, r.WithContext(ctx))
	}
}
