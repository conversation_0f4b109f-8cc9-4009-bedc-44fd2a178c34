package rest

import (
	"fmt"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type HTTPMiddleware interface {
	AlwaysDeny(w http.ResponseWriter, r *http.Request) error
	Authenticated(next http.HandlerFunc) http.HandlerFunc
	HasAccess(w http.ResponseWriter, r *http.Request) error
	CorrelationID(next http.Handler) http.Handler
	Cors(next http.Handler) http.Handler
	Logging(next http.Handler) http.Handler
	Client(next http.Handler) http.Handler
}

type httpMiddleware struct {
	Log         *logrus.Logger
	Config      *viper.Viper
	UserUsecase model.UserUsecase
}

// AlwaysDeny implements HTTPMiddleware.
func (h *httpMiddleware) AlwaysDeny(w http.ResponseWriter, r *http.Request) error {
	err := fmt.Errorf("Always deny")
	rest.ErrorResponse(w, r, utils.Unauthorizedf("Unauthorized", err, nil), http.StatusUnauthorized)
	return err
}

// HasAccess implements HTTPMiddleware.
func (h *httpMiddleware) HasAccess(w http.ResponseWriter, r *http.Request) error {
	// TODO: check if user has access to the resource
	return nil
}

func NewHTTPMiddleware(
	log *logrus.Logger,
	config *viper.Viper,
	userUsecase model.UserUsecase,
) HTTPMiddleware {
	return &httpMiddleware{
		Log:         log,
		Config:      config,
		UserUsecase: userUsecase,
	}
}
