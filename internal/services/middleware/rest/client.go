package rest

import (
	"context"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	utilsRest "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Client implements HTTPMiddleware.
func (h *httpMiddleware) Client(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// TODO: get client tenant

		ctx := context.WithValue(r.Context(), utils.TenantIDKey, utilsRest.DevTenant)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
