package rest

import (
	"context"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	utilsRest "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// CorrelationID implements HTTPMiddleware.
func (h *httpMiddleware) CorrelationID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		correlationID := utils.UniqueId()

		w.Header().Set(utilsRest.XCorrelationID, correlationID)

		// Add correlation ID to the context and proceed
		ctx := context.WithValue(r.Context(), utils.CorrelationIDKey, correlationID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
