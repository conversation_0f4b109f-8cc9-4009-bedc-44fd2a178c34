package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.ChannelUsecase.
func (c *channelUsecase) Create(ctx context.Context, channel model.ChannelCreate) (string, error) {
	// Check if name already exists
	nameExists, err := c.repo.CountByProp(ctx, "name", channel.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if channel name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ChannelConflictNamef("Channel name already exists", nil, nil)
	}

	// Generate ID and set default state if not provided
	id := utils.UniqueId()
	state := channel.State
	if state == "" {
		state = "active"
	}

	channelModel := model.Channel{
		ID:    id,
		Name:  channel.Name,
		State: state,
	}

	err = c.repo.Create(ctx, channelModel)
	if err != nil {
		return "", utils.InternalErrorf("Failed to create channel", err, nil)
	}

	return id, nil
}
