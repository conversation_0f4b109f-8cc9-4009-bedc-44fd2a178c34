package app

import (
	"context"
	"errors"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.ChannelUsecase.
func (c *channelUsecase) Update(ctx context.Context, channel model.ChannelUpdate) error {
	// Check if channel exists
	_, err := c.repo.GetByProp(ctx, "id", channel.ID)
	if err != nil {
		return err
	}

	// Check if name already exists for a different channel
	existingChannel, err := c.repo.GetByProp(ctx, "name", channel.Name)
	if err != nil {
		var appErr utils.AppErr
		if errors.As(err, &appErr) && appErr.Code != model.ChannelNotFoundCode {
			return utils.InternalErrorf("Failed to check if channel name exists", err, nil)
		} else if !errors.As(err, &appErr) {
			return utils.InternalErrorf("Failed to check if channel name exists", err, nil)
		}
	}

	if existingChannel != nil && existingChannel.ID != channel.ID {
		return model.ChannelConflictNamef("Channel name already exists", nil, nil)
	}

	channelModel := model.Channel{
		ID:    channel.ID,
		Name:  channel.Name,
		State: channel.State,
	}

	err = c.repo.Update(ctx, channelModel)
	if err != nil {
		return utils.InternalErrorf("Failed to update channel", err, nil)
	}

	return nil
}
