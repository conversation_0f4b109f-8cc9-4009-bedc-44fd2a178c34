package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
)

type channelUsecase struct {
	repo model.ChannelRepository
}

// Delete implements model.ChannelUsecase.
func (c *channelUsecase) Delete(ctx context.Context, id string) error {
	return c.repo.Delete(ctx, id)
}

// GetAll implements model.ChannelUsecase.
func (c *channelUsecase) GetAll(ctx context.Context) ([]model.Channel, error) {
	return c.repo.GetAll(ctx)
}

// GetByProp implements model.ChannelUsecase.
func (c *channelUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Channel, error) {
	return c.repo.GetByProp(ctx, prop, value)
}

// ValidateName implements model.ChannelUsecase.
func (c *channelUsecase) ValidateName(ctx context.Context, name string) error {
	count, err := c.repo.CountByProp(ctx, "name", name)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ChannelConflictNamef("Channel name already exists", nil, nil)
	}

	return nil
}

func NewChannelUsecase(repo model.ChannelRepository) model.ChannelUsecase {
	return &channelUsecase{
		repo: repo,
	}
}
