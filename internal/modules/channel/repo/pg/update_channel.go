package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *channelPostgreRepo) Update(ctx context.Context, channel model.Channel) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE channels 
			SET name = $2, state = $3, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			channel.ID,
			channel.Name,
			channel.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update channel", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.ChannelNotFoundf("Channel not found", nil, nil)
		}

		return nil
	})
}
