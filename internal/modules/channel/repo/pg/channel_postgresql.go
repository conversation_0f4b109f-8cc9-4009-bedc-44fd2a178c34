package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ChannelPostgreRepo interface {
	Create(ctx context.Context, channel model.Channel) error
	Update(ctx context.Context, channel model.Channel) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Channel, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Channel, error)
	Delete(ctx context.Context, id string) error
}

type channelPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewChannelPostgreRepo(pool *pgxpool.Pool) ChannelPostgreRepo {
	return &channelPostgreRepo{
		pool: pool,
	}
}
