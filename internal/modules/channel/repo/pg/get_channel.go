package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *channelPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Channel, error) {
	var channel model.Channel
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, name, state, created_at, updated_at, deleted_at
			FROM channels 
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&channel.ID,
			&channel.Name,
			&channel.State,
			&channel.CreatedAt,
			&channel.UpdatedAt,
			&channel.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.ChannelNotFoundf("Channel not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get channel", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &channel, nil
}

func (c *channelPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT COUNT(*) 
			FROM channels 
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count channels", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (c *channelPostgreRepo) GetAll(ctx context.Context) ([]model.Channel, error) {
	var channels []model.Channel
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, state, created_at, updated_at, deleted_at
			FROM channels 
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get channels", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var channel model.Channel
			err := rows.Scan(
				&channel.ID,
				&channel.Name,
				&channel.State,
				&channel.CreatedAt,
				&channel.UpdatedAt,
				&channel.DeletedAt,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan channel", err, nil)
			}

			channels = append(channels, channel)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return channels, nil
}
