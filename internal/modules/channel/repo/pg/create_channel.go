package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *channelPostgreRepo) Create(ctx context.Context, channel model.Channel) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO channels (id, name, state)
			VALUES ($1, $2, $3)
		`

		_, err := conn.Exec(ctx, query,
			channel.ID,
			channel.Name,
			channel.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create channel", err, nil)
		}

		return nil
	})
}
