package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/repo/pg"
)

type channelRepository struct {
	pgRepo pg.ChannelPostgreRepo
}

// CountByProp implements model.ChannelRepository.
func (c *channelRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return c.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.ChannelRepository.
func (c *channelRepository) Create(ctx context.Context, channel model.Channel) error {
	return c.pgRepo.Create(ctx, channel)
}

// Delete implements model.ChannelRepository.
func (c *channelRepository) Delete(ctx context.Context, id string) error {
	return c.pgRepo.Delete(ctx, id)
}

// GetAll implements model.ChannelRepository.
func (c *channelRepository) GetAll(ctx context.Context) ([]model.Channel, error) {
	return c.pgRepo.GetAll(ctx)
}

// GetByProp implements model.ChannelRepository.
func (c *channelRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Channel, error) {
	return c.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.ChannelRepository.
func (c *channelRepository) Update(ctx context.Context, channel model.Channel) error {
	return c.pgRepo.Update(ctx, channel)
}

func NewChannelRepository(pgRepo pg.ChannelPostgreRepo) model.ChannelRepository {
	return &channelRepository{
		pgRepo: pgRepo,
	}
}
