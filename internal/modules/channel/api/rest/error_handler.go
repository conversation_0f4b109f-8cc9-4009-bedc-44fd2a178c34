package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ChannelConflictCode:     http.StatusConflict,
	model.ChannelConflictNameCode: http.StatusConflict,
	model.ChannelNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
