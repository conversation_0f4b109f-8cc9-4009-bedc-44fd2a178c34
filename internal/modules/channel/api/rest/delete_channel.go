package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements ChannelHandler.
func (c *channelHandler) Delete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Channel ID is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := c.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to delete channel")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
