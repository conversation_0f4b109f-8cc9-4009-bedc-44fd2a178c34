package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateName implements ChannelHandler.
func (c *channelHandler) ValidateName(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	name := r.PathValue("name")

	if name == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Channel name is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := c.useCase.ValidateName(ctx, name)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Channel name validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
