package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type channelCreate struct {
	Name  string `json:"name" validate:"required"`
	State string `json:"state"`
}

func channelCreateToModel(dto channelCreate) model.ChannelCreate {
	return model.ChannelCreate{
		Name:  dto.Name,
		State: dto.State,
	}
}

// Create implements ChannelHandler.
func (c *channelHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[channelCreate](w, r, c.validator)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		return
	}

	id, err := c.useCase.Create(ctx, channelCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to create channel")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
