package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type channelResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	State     string     `json:"state"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func channelToResult(channel model.Channel) channelResult {
	return channelResult{
		ID:        channel.ID,
		Name:      channel.Name,
		State:     channel.State,
		CreatedAt: channel.CreatedAt,
		UpdatedAt: channel.UpdatedAt,
		DeletedAt: channel.DeletedAt,
	}
}

// GetById implements ChannelHandler.
func (c *channelHandler) GetById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Channel ID is required", nil, nil), http.StatusBadRequest)
		return
	}

	channel, err := c.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get channel")
		return
	}

	rest.SuccessDResponse(w, r, channelToResult(*channel), http.StatusOK)
}

// GetAll implements ChannelHandler.
func (c *channelHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	channels, err := c.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get channels")
		return
	}

	var results []channelResult
	for _, channel := range channels {
		results = append(results, channelToResult(channel))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
