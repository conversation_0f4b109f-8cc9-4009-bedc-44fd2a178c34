package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/channel/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ChannelHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateName(w http.ResponseWriter, r *http.Request)
}

type channelHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.ChannelUsecase
}

func NewChannelHandler(log *logrus.Logger, validator *validator.Validate, useCase model.ChannelUsecase) ChannelHandler {
	return &channelHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
