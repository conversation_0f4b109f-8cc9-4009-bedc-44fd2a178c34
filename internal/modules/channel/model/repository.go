package model

import "context"

type ChannelRepository interface {
	Create(ctx context.Context, channel Channel) error
	Update(ctx context.Context, channel Channel) error
	GetByProp(ctx context.Context, prop string, value string) (*Channel, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Channel, error)
	Delete(ctx context.Context, id string) error
}
