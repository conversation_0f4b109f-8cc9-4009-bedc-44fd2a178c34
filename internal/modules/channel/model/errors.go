package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ChannelConflictCode     utils.ErrCode = utils.ChannelCode + iota
	ChannelConflictNameCode
	ChannelNotFoundCode
)

func ChannelConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ChannelConflictCode, message, err, details)
}

func ChannelConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ChannelConflictNameCode, message, err, details)
}

func ChannelNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ChannelNotFoundCode, message, err, details)
}
