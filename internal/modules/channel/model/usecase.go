package model

import "context"

type ChannelUsecase interface {
	Create(ctx context.Context, channel ChannelCreate) (string, error)
	Update(ctx context.Context, channel ChannelUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Channel, error)
	GetAll(ctx context.Context) ([]Channel, error)
	Delete(ctx context.Context, id string) error
	ValidateName(ctx context.Context, name string) error
}
