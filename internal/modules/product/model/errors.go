package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ProductConflictCode     utils.ErrCode = utils.ProductCode + iota
	ProductConflictNameCode
	ProductConflictCodeCode
	ProductConflictSKUCode
	ProductNotFoundCode
	ProductInvalidMeasurementUnitCode
	ProductInvalidCategoryCode
	ProductInvalidBrandCode
)

func ProductConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductConflictCode, message, err, details)
}

func ProductConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductConflictNameCode, message, err, details)
}

func ProductConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductConflictCodeCode, message, err, details)
}

func ProductConflictSKUf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductConflictSKUCode, message, err, details)
}

func ProductNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductNotFoundCode, message, err, details)
}

func ProductInvalidMeasurementUnitf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductInvalidMeasurementUnitCode, message, err, details)
}

func ProductInvalidCategoryf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductInvalidCategoryCode, message, err, details)
}

func ProductInvalidBrandf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ProductInvalidBrandCode, message, err, details)
}
