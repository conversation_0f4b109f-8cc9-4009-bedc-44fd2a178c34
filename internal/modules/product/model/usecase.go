package model

import "context"

type ProductUsecase interface {
	Create(ctx context.Context, product ProductCreate) (string, error)
	Update(ctx context.Context, product ProductUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Product, error)
	GetAll(ctx context.Context) ([]Product, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	ValidateCommercialName(ctx context.Context, commercialName string) error
	ValidateSKUCode(ctx context.Context, skuCode string) error
	GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]Product, error)
}
