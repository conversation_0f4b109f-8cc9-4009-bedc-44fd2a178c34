package model

import "context"

type ProductRepository interface {
	Create(ctx context.Context, product Product) error
	Update(ctx context.Context, product Product) error
	GetByProp(ctx context.Context, prop string, value string) (*Product, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Product, error)
	Delete(ctx context.Context, id string) error
	GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]Product, error)
	CreateProductCategories(ctx context.Context, productID string, categoryIDs []string) error
	UpdateProductCategories(ctx context.Context, productID string, categoryIDs []string) error
	GetProductCategories(ctx context.Context, productID string) ([]string, error)
	DeleteProductCategories(ctx context.Context, productID string) error
}
