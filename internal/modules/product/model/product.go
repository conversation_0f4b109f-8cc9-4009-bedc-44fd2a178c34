package model

import "time"

type Product struct {
	ID                string
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryIDs       []string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
	CostPriceTotal    int
	ProductionInfo    *ProductionInfo
	CreatedAt         *time.Time
	UpdatedAt         *time.Time
	DeletedAt         *time.Time
}

type ProductCreate struct {
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryIDs       []string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
	CostPriceTotal    int
	ProductionInfo    *ProductionInfo
}

type ProductUpdate struct {
	ID                string
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryIDs       []string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
	CostPriceTotal    int
	ProductionInfo    *ProductionInfo
}
