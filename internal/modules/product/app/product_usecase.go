package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
)

type productUsecase struct {
	repo model.ProductRepository
}

// Delete implements model.ProductUsecase.
func (p *productUsecase) Delete(ctx context.Context, id string) error {
	return p.repo.Delete(ctx, id)
}

// GetAll implements model.ProductUsecase.
func (p *productUsecase) GetAll(ctx context.Context) ([]model.Product, error) {
	return p.repo.GetAll(ctx)
}

// GetByProp implements model.ProductUsecase.
func (p *productUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Product, error) {
	return p.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.ProductUsecase.
func (p *productUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := p.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ProductConflictCodef("Product code already exists", nil, nil)
	}

	return nil
}

// GetProductsByCategoryCode implements model.ProductUsecase.
func (p *productUsecase) GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]model.Product, error) {
	return p.repo.GetProductsByCategoryCode(ctx, categoryCode)
}

// ValidateCommercialName implements model.ProductUsecase.
func (p *productUsecase) ValidateCommercialName(ctx context.Context, commercialName string) error {
	count, err := p.repo.CountByProp(ctx, "commercial_name", commercialName)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ProductConflictNamef("Product commercial name already exists", nil, nil)
	}

	return nil
}

// ValidateSKUCode implements model.ProductUsecase.
func (p *productUsecase) ValidateSKUCode(ctx context.Context, skuCode string) error {
	count, err := p.repo.CountByProp(ctx, "sku_code", skuCode)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ProductConflictSKUf("Product SKU code already exists", nil, nil)
	}

	return nil
}

func NewProductUsecase(repo model.ProductRepository) model.ProductUsecase {
	return &productUsecase{
		repo: repo,
	}
}
