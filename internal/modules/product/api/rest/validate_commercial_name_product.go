package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCommercialName implements ProductHandler.
func (p *productHandler) ValidateCommercialName(w http.ResponseWriter, r *http.Request) {
	commercialName := r.PathValue("commercialName")
	ctx := r.Context()

	if commercialName == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Commercial name parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := p.useCase.ValidateCommercialName(ctx, commercialName)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Commercial name validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
