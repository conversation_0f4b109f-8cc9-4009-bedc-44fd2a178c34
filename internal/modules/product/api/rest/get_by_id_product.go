package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements ProductHandler.
func (p *productHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	product, err := p.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get product by id")
		return
	}

	rest.SuccessDResponse(w, r, productToResult(product), http.StatusOK)
}
