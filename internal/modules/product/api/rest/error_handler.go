package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ProductConflictCode:                 http.StatusConflict,
	model.ProductConflictNameCode:             http.StatusConflict,
	model.ProductConflictCodeCode:             http.StatusConflict,
	model.ProductConflictSKUCode:              http.StatusConflict,
	model.ProductNotFoundCode:                 http.StatusNotFound,
	model.ProductInvalidMeasurementUnitCode:   http.StatusBadRequest,
	model.ProductInvalidCategoryCode:          http.StatusBadRequest,
	model.ProductInvalidBrandCode:             http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
