package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements ProductHandler.
func (p *productHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	products, err := p.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get all products")
		return
	}

	results := make([]productResult, len(products))
	for i, product := range products {
		results[i] = productToResult(&product)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
