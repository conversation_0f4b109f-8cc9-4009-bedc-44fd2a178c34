package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
)

type productResult struct {
	ID                string                   `json:"id"`
	Name              string                   `json:"name"`
	ImageURL          *string                  `json:"image_url"`
	CommercialName    string                   `json:"commercial_name"`
	Code              string                   `json:"code"`
	SKUCode           string                   `json:"sku_code"`
	MeasurementUnitID string                   `json:"measurement_unit_id"`
	CategoryIDs       []string                 `json:"category_ids"`
	BrandID           string                   `json:"brand_id"`
	State             string                   `json:"state"`
	Description       *string                  `json:"description"`
	CanBeSold         bool                     `json:"can_be_sold"`
	CanBePurchased    bool                     `json:"can_be_purchased"`
	CostPrice         *float64                 `json:"cost_price"`
	CostPriceTotal    int                      `json:"cost_price_total"`
	ProductionInfo    *productionInfoResultDTO `json:"production_info"`
	CreatedAt         *time.Time               `json:"created_at"`
	UpdatedAt         *time.Time               `json:"updated_at"`
	DeletedAt         *time.Time               `json:"deleted_at"`
}

type productionInfoResultDTO struct {
	ProductionType    string              `json:"production_type"`
	Materials         []materialResultDTO `json:"materials"`
	UnitQuantity      *float64            `json:"unit_quantity"`
	MeasurementUnitID *string             `json:"measurement_unit_id"`
}

type materialResultDTO struct {
	ProductID string  `json:"product_id"`
	Quantity  float64 `json:"quantity"`
}

func productToResult(product *model.Product) productResult {
	var productionInfo *productionInfoResultDTO
	if product.ProductionInfo != nil {
		materials := make([]materialResultDTO, len(product.ProductionInfo.Materials))
		for i, material := range product.ProductionInfo.Materials {
			materials[i] = materialResultDTO{
				ProductID: material.ProductID,
				Quantity:  material.Quantity,
			}
		}
		productionInfo = &productionInfoResultDTO{
			ProductionType:    product.ProductionInfo.ProductionType,
			Materials:         materials,
			UnitQuantity:      product.ProductionInfo.UnitQuantity,
			MeasurementUnitID: product.ProductionInfo.MeasurementUnitID,
		}
	}

	return productResult{
		ID:                product.ID,
		Name:              product.Name,
		ImageURL:          product.ImageURL,
		CommercialName:    product.CommercialName,
		Code:              product.Code,
		SKUCode:           product.SKUCode,
		MeasurementUnitID: product.MeasurementUnitID,
		CategoryIDs:       product.CategoryIDs,
		BrandID:           product.BrandID,
		State:             product.State,
		Description:       product.Description,
		CanBeSold:         product.CanBeSold,
		CanBePurchased:    product.CanBePurchased,
		CostPrice:         product.CostPrice,
		CostPriceTotal:    product.CostPriceTotal,
		ProductionInfo:    productionInfo,
		CreatedAt:         product.CreatedAt,
		UpdatedAt:         product.UpdatedAt,
		DeletedAt:         product.DeletedAt,
	}
}
