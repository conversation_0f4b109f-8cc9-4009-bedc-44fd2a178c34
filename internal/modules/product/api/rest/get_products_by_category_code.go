package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetProductsByCategoryCode implements ProductHandler.
func (p *productHandler) GetProductsByCategoryCode(w http.ResponseWriter, r *http.Request) {
	categoryCode := r.PathValue("categoryCode")
	ctx := r.Context()

	if categoryCode == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Category code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	products, err := p.useCase.GetProductsByCategoryCode(ctx, categoryCode)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get products by category code")
		return
	}

	results := make([]productResult, len(products))
	for i, product := range products {
		results[i] = productToResult(&product)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
