package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateSKUCode implements ProductHandler.
func (p *productHandler) ValidateSKUCode(w http.ResponseWriter, r *http.Request) {
	skuCode := r.PathValue("skuCode")
	ctx := r.Context()

	if skuCode == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("SKU code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := p.useCase.ValidateSKUCode(ctx, skuCode)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "SKU code validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
