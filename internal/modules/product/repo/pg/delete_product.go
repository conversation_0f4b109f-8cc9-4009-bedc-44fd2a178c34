package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE products SET
				deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete product", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.ProductNotFoundf("Product not found", nil, nil)
		}

		return nil
	})
}
