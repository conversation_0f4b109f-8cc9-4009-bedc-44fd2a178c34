-- Migration script to add production_info table for products
-- This table stores production information including production type and materials

-- Step 1: Create the production_info table
CREATE TABLE IF NOT EXISTS dev.production_info (
    id VARCHAR(255) PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL UNIQUE,
    production_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES dev.products(id) ON DELETE CASCADE
);

-- Step 2: Create the production_info_materials table for materials
CREATE TABLE IF NOT EXISTS dev.production_info_materials (
    id VARCHAR(255) PRIMARY KEY,
    production_info_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,4) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON><PERSON><PERSON> KEY (production_info_id) REFERENCES dev.production_info(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES dev.products(id) ON DELETE CASCADE
);

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_production_info_product_id ON dev.production_info(product_id);
CREATE INDEX IF NOT EXISTS idx_production_info_materials_production_info_id ON dev.production_info_materials(production_info_id);
CREATE INDEX IF NOT EXISTS idx_production_info_materials_product_id ON dev.production_info_materials(product_id);

-- Step 4: Add comments to document the tables
COMMENT ON TABLE dev.production_info IS 'Production information for products including production type';
COMMENT ON TABLE dev.production_info_materials IS 'Materials required for product production';
COMMENT ON COLUMN dev.production_info.production_type IS 'Type of production (bulk, unit)';
COMMENT ON COLUMN dev.production_info_materials.quantity IS 'Quantity of material needed for production';
