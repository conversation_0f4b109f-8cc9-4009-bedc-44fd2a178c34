-- Migration script to add unit_quantity and measurement_unit_id to production_info table
-- These fields are nullable to support existing production info records

-- Step 1: Add unit_quantity column to production_info table
ALTER TABLE dev.production_info 
ADD COLUMN IF NOT EXISTS unit_quantity DECIMAL(10,4);

-- Step 2: Add measurement_unit_id column to production_info table
ALTER TABLE dev.production_info 
ADD COLUMN IF NOT EXISTS measurement_unit_id VARCHAR(255);

-- Step 3: Add foreign key constraint for measurement_unit_id (if not null)
-- Note: This constraint allows NULL values but validates non-NULL values
ALTER TABLE dev.production_info 
ADD CONSTRAINT fk_production_info_measurement_unit 
FOREIGN KEY (measurement_unit_id) REFERENCES dev.measurement_units(id);

-- Step 4: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_production_info_measurement_unit_id ON dev.production_info(measurement_unit_id);

-- Step 5: Add comments to document the new columns
COMMENT ON COLUMN dev.production_info.unit_quantity IS 'Quantity per unit of production (nullable)';
COMMENT ON COLUMN dev.production_info.measurement_unit_id IS 'Reference to measurement unit for unit quantity (nullable)';
