-- Migration script to add cost_price_total column to products table
-- This adds a simple integer field for tracking total cost price

-- Step 1: Add the cost_price_total column to products table
ALTER TABLE dev.products 
ADD COLUMN IF NOT EXISTS cost_price_total INTEGER;

-- Step 2: Create index for better performance (optional, if queries will filter by this column)
CREATE INDEX IF NOT EXISTS idx_products_cost_price_total ON dev.products(cost_price_total);

-- Step 3: Add comment to document the column purpose
COMMENT ON COLUMN dev.products.cost_price_total IS 'Total cost price as integer value';