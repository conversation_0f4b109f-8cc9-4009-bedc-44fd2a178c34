package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) GetAll(ctx context.Context) ([]model.Product, error) {
	var products []model.Product

	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.brand_id, p.state,
				p.description, p.can_be_sold, p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
			FROM products p
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE p.deleted_at IS NULL
			GROUP BY p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
					 p.measurement_unit_id, p.brand_id, p.state,
					 p.description, p.can_be_sold, p.can_be_purchased, p.cost_price,
					 p.created_at, p.updated_at, p.deleted_at
			ORDER BY p.created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all products", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var product model.Product

			err := rows.Scan(
				&product.ID,
				&product.Name,
				&product.ImageURL,
				&product.CommercialName,
				&product.Code,
				&product.SKUCode,
				&product.MeasurementUnitID,
				&product.BrandID,
				&product.State,
				&product.Description,
				&product.CanBeSold,
				&product.CanBePurchased,
				&product.CostPrice,
				&product.CostPriceTotal,
				&product.CreatedAt,
				&product.UpdatedAt,
				&product.DeletedAt,
				&product.CategoryIDs,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan product", err, nil)
			}

			products = append(products, product)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate products", err, nil)
		}

		// Get production info for all products
		for i := range products {
			productionInfoQuery := `
				SELECT pi.production_type, pi.unit_quantity, pi.measurement_unit_id
				FROM production_info pi
				WHERE pi.product_id = $1
			`
			var productionType string
			var unitQuantity *float64
			var measurementUnitID *string
			err := conn.QueryRow(ctx, productionInfoQuery, products[i].ID).Scan(&productionType, &unitQuantity, &measurementUnitID)
			if err == nil {
				// Production info exists, get materials
				materialsQuery := `
					SELECT pim.product_id, pim.quantity
					FROM production_info_materials pim
					JOIN production_info pi ON pim.production_info_id = pi.id
					WHERE pi.product_id = $1
					ORDER BY pim.created_at ASC
				`
				materialRows, err := conn.Query(ctx, materialsQuery, products[i].ID)
				if err != nil {
					return utils.InternalErrorf("failed to get production info materials", err, nil)
				}
				defer materialRows.Close()

				var materials []model.Material
				for materialRows.Next() {
					var material model.Material
					err := materialRows.Scan(&material.ProductID, &material.Quantity)
					if err != nil {
						return utils.InternalErrorf("failed to scan production info material", err, nil)
					}
					materials = append(materials, material)
				}

				if err := materialRows.Err(); err != nil {
					return utils.InternalErrorf("failed to iterate production info materials", err, nil)
				}

				products[i].ProductionInfo = &model.ProductionInfo{
					ProductID:         products[i].ID,
					ProductionType:    productionType,
					UnitQuantity:      unitQuantity,
					MeasurementUnitID: measurementUnitID,
					Materials:         materials,
				}
			} else if err != pgx.ErrNoRows {
				return utils.InternalErrorf("failed to get production info", err, nil)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return products, nil
}
