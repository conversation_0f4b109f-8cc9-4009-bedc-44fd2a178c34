package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ProductPostgreRepo interface {
	Create(ctx context.Context, product model.Product) error
	Update(ctx context.Context, product model.Product) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Product, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Product, error)
	Delete(ctx context.Context, id string) error
	GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]model.Product, error)
	CreateProductCategories(ctx context.Context, productID string, categoryIDs []string) error
	UpdateProductCategories(ctx context.Context, productID string, categoryIDs []string) error
	GetProductCategories(ctx context.Context, productID string) ([]string, error)
	DeleteProductCategories(ctx context.Context, productID string) error
}

type productPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewProductPostgreRepo(pool *pgxpool.Pool) ProductPostgreRepo {
	return &productPostgreRepo{
		pool: pool,
	}
}
