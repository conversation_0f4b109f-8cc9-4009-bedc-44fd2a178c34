package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM products
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count products by prop", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}
