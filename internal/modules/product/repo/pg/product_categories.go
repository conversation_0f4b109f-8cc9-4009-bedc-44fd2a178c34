package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (p *productPostgreRepo) CreateProductCategories(ctx context.Context, productID string, categoryIDs []string) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		for _, categoryID := range categoryIDs {
			id := ulid.Make().String()
			query := `
				INSERT INTO product_categories (id, product_id, category_id)
				VALUES ($1, $2, $3)
				ON CONFLICT (product_id, category_id) DO NOTHING
			`
			_, err := conn.Exec(ctx, query, id, productID, categoryID)
			if err != nil {
				return utils.InternalErrorf("failed to create product category relationship", err, nil)
			}
		}
		return nil
	})
}

func (p *productPostgreRepo) UpdateProductCategories(ctx context.Context, productID string, categoryIDs []string) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// First, delete existing relationships
		deleteQuery := `DELETE FROM product_categories WHERE product_id = $1`
		_, err := conn.Exec(ctx, deleteQuery, productID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing product categories", err, nil)
		}

		// Then, create new relationships
		for _, categoryID := range categoryIDs {
			id := ulid.Make().String()
			insertQuery := `
				INSERT INTO product_categories (id, product_id, category_id)
				VALUES ($1, $2, $3)
			`
			_, err := conn.Exec(ctx, insertQuery, id, productID, categoryID)
			if err != nil {
				return utils.InternalErrorf("failed to create product category relationship", err, nil)
			}
		}
		return nil
	})
}

func (p *productPostgreRepo) GetProductCategories(ctx context.Context, productID string) ([]string, error) {
	var categoryIDs []string
	
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT category_id
			FROM product_categories
			WHERE product_id = $1
			ORDER BY created_at ASC
		`
		
		rows, err := conn.Query(ctx, query, productID)
		if err != nil {
			return utils.InternalErrorf("failed to get product categories", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var categoryID string
			err := rows.Scan(&categoryID)
			if err != nil {
				return utils.InternalErrorf("failed to scan category ID", err, nil)
			}
			categoryIDs = append(categoryIDs, categoryID)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate product categories", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return categoryIDs, nil
}

func (p *productPostgreRepo) DeleteProductCategories(ctx context.Context, productID string) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `DELETE FROM product_categories WHERE product_id = $1`
		_, err := conn.Exec(ctx, query, productID)
		if err != nil {
			return utils.InternalErrorf("failed to delete product categories", err, nil)
		}
		return nil
	})
}
