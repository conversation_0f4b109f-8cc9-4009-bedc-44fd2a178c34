package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type warehouseCreate struct {
	Name              string  `json:"name" validate:"required"`
	Code              string  `json:"code" validate:"required"`
	Type              string  `json:"type" validate:"required"`
	Category          string  `json:"category" validate:"required"`
	Description       *string `json:"description"`
	Address           *string `json:"address"`
	IsActive          bool    `json:"is_active"`
	IsSystemWarehouse bool    `json:"is_system_warehouse"`
}

func warehouseCreateToModel(dto warehouseCreate) model.WarehouseCreate {
	return model.WarehouseCreate{
		Name:              dto.Name,
		Code:              dto.Code,
		Type:              dto.Type,
		Category:          dto.Category,
		Description:       dto.Description,
		Address:           dto.Address,
		IsActive:          dto.IsActive,
		IsSystemWarehouse: dto.IsSystemWarehouse,
	}
}

// Create implements WarehouseHandler.
func (w *warehouseHandler) Create(wr http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[warehouseCreate](wr, r, w.validator)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		return
	}

	id, err := w.useCase.Create(ctx, warehouseCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to create warehouse")
		return
	}

	rest.SuccessDResponse(wr, r, id, http.StatusCreated)
}
