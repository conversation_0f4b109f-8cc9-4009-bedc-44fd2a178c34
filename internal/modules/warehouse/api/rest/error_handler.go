package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.WarehouseConflictCode:     http.StatusConflict,
	model.WarehouseConflictNameCode: http.StatusConflict,
	model.WarehouseConflictCodeCode: http.StatusConflict,
	model.WarehouseNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
