package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type warehouseResult struct {
	ID                string     `json:"id"`
	Name              string     `json:"name"`
	Code              string     `json:"code"`
	Type              string     `json:"type"`
	Category          string     `json:"category"`
	Description       *string    `json:"description"`
	Address           *string    `json:"address"`
	IsActive          bool       `json:"is_active"`
	IsSystemWarehouse bool       `json:"is_system_warehouse"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
	DeletedAt         *time.Time `json:"deleted_at"`
}

func warehouseToResult(warehouse *model.Warehouse) warehouseResult {
	return warehouseResult{
		ID:                warehouse.ID,
		Name:              warehouse.Name,
		Code:              warehouse.Code,
		Type:              warehouse.Type,
		Category:          warehouse.Category,
		Description:       warehouse.Description,
		Address:           warehouse.Address,
		IsActive:          warehouse.IsActive,
		IsSystemWarehouse: warehouse.IsSystemWarehouse,
		CreatedAt:         warehouse.CreatedAt,
		UpdatedAt:         warehouse.UpdatedAt,
		DeletedAt:         warehouse.DeletedAt,
	}
}

// GetById implements WarehouseHandler.
func (w *warehouseHandler) GetById(wr http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	warehouse, err := w.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to get warehouse by id")
		return
	}

	rest.SuccessDResponse(wr, r, warehouseToResult(warehouse), http.StatusOK)
}
