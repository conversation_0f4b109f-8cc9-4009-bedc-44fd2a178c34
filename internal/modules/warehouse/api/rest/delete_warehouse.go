package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements WarehouseHandler.
func (w *warehouseHandler) Delete(wr http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if err := w.useCase.Delete(ctx, id); err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to delete warehouse")
		return
	}

	rest.SuccessResponse(wr, r, http.StatusNoContent)
}
