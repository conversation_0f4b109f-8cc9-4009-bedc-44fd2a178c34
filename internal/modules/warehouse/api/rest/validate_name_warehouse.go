package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateName implements WarehouseHandler.
func (w *warehouseHandler) ValidateName(wr http.ResponseWriter, r *http.Request) {
	name := r.<PERSON>alue("name")
	ctx := r.Context()

	if name == "" {
		rest.ErrorResponse(wr, r, utils.BadRequestf("Name parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := w.useCase.ValidateName(ctx, name)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Name validation failed")
		return
	}

	rest.SuccessResponse(wr, r, http.StatusOK)
}
