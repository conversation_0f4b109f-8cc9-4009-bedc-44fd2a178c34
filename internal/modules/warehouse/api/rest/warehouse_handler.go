package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type WarehouseHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	ValidateName(w http.ResponseWriter, r *http.Request)
}

type warehouseHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.WarehouseUsecase
}

func NewWarehouseHandler(log *logrus.Logger, validator *validator.Validate, useCase model.WarehouseUsecase) WarehouseHandler {
	return &warehouseHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
