package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type warehouseUpdate struct {
	ID                string  `json:"id" validate:"required"`
	Name              string  `json:"name" validate:"required"`
	Code              string  `json:"code" validate:"required"`
	Type              string  `json:"type" validate:"required"`
	Category          string  `json:"category" validate:"required"`
	Description       *string `json:"description"`
	Address           *string `json:"address"`
	IsActive          bool    `json:"is_active"`
	IsSystemWarehouse bool    `json:"is_system_warehouse"`
}

func warehouseUpdateToModel(dto warehouseUpdate) model.WarehouseUpdate {
	return model.WarehouseUpdate{
		ID:                dto.ID,
		Name:              dto.Name,
		Code:              dto.Code,
		Type:              dto.Type,
		Category:          dto.Category,
		Description:       dto.Description,
		Address:           dto.Address,
		IsActive:          dto.IsActive,
		IsSystemWarehouse: dto.IsSystemWarehouse,
	}
}

// Update implements WarehouseHandler.
func (w *warehouseHandler) Update(wr http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[warehouseUpdate](wr, r, w.validator)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		return
	}

	if err := w.useCase.Update(ctx, warehouseUpdateToModel(*req)); err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to update warehouse")
		return
	}

	rest.SuccessResponse(wr, r, http.StatusNoContent)
}
