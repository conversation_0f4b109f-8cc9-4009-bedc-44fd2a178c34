package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements WarehouseHandler.
func (w *warehouseHandler) GetAll(wr http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	warehouses, err := w.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to get warehouses")
		return
	}

	var results []warehouseResult
	for _, warehouse := range warehouses {
		results = append(results, warehouseToResult(&warehouse))
	}

	rest.SuccessDResponse(wr, r, results, http.StatusOK)
}
