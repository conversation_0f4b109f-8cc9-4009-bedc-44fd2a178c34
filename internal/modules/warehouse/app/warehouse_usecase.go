package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
)

type warehouseUsecase struct {
	repo model.WarehouseRepository
}

// Delete implements model.WarehouseUsecase.
func (w *warehouseUsecase) Delete(ctx context.Context, id string) error {
	return w.repo.Delete(ctx, id)
}

// GetAll implements model.WarehouseUsecase.
func (w *warehouseUsecase) GetAll(ctx context.Context) ([]model.Warehouse, error) {
	return w.repo.GetAll(ctx)
}

// GetByProp implements model.WarehouseUsecase.
func (w *warehouseUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Warehouse, error) {
	return w.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.WarehouseUsecase.
func (w *warehouseUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := w.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.WarehouseConflictCodef("Warehouse code already exists", nil, nil)
	}

	return nil
}

// ValidateName implements model.WarehouseUsecase.
func (w *warehouseUsecase) ValidateName(ctx context.Context, name string) error {
	count, err := w.repo.CountByProp(ctx, "name", name)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.WarehouseConflictNamef("Warehouse name already exists", nil, nil)
	}

	return nil
}

func NewWarehouseUsecase(repo model.WarehouseRepository) model.WarehouseUsecase {
	return &warehouseUsecase{
		repo: repo,
	}
}
