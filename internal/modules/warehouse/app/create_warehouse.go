package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.WarehouseUsecase.
func (w *warehouseUsecase) Create(ctx context.Context, warehouse model.WarehouseCreate) (string, error) {
	// Check if name already exists
	nameExists, err := w.repo.CountByProp(ctx, "name", warehouse.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if warehouse name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.WarehouseConflictNamef("Warehouse name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := w.repo.CountByProp(ctx, "code", warehouse.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if warehouse code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.WarehouseConflictCodef("Warehouse code already exists", nil, nil)
	}

	// Create warehouse entity
	id := ulid.Make().String()
	warehouseEntity := model.Warehouse{
		ID:               id,
		Name:             warehouse.Name,
		Code:             warehouse.Code,
		Type:             warehouse.Type,
		Category:         warehouse.Category,
		Description:      warehouse.Description,
		Address:          warehouse.Address,
		IsActive:         warehouse.IsActive,
		IsSystemWarehouse: warehouse.IsSystemWarehouse,
	}

	// Save to repository
	err = w.repo.Create(ctx, warehouseEntity)
	if err != nil {
		return "", utils.InternalErrorf("Failed to create warehouse", err, nil)
	}

	return id, nil
}
