package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.WarehouseUsecase.
func (w *warehouseUsecase) Update(ctx context.Context, warehouse model.WarehouseUpdate) error {
	// Check if warehouse exists
	existingWarehouse, err := w.repo.GetByProp(ctx, "id", warehouse.ID)
	if err != nil {
		return err
	}

	// Check if name already exists (excluding current warehouse)
	if existingWarehouse.Name != warehouse.Name {
		nameExists, err := w.repo.CountByProp(ctx, "name", warehouse.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if warehouse name exists", err, nil)
		}

		if nameExists > 0 {
			return model.WarehouseConflictNamef("Warehouse name already exists", nil, nil)
		}
	}

	// Check if code already exists (excluding current warehouse)
	if existingWarehouse.Code != warehouse.Code {
		codeExists, err := w.repo.CountByProp(ctx, "code", warehouse.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if warehouse code exists", err, nil)
		}

		if codeExists > 0 {
			return model.WarehouseConflictCodef("Warehouse code already exists", nil, nil)
		}
	}

	// Create warehouse entity for update
	warehouseEntity := model.Warehouse{
		ID:               warehouse.ID,
		Name:             warehouse.Name,
		Code:             warehouse.Code,
		Type:             warehouse.Type,
		Category:         warehouse.Category,
		Description:      warehouse.Description,
		Address:          warehouse.Address,
		IsActive:         warehouse.IsActive,
		IsSystemWarehouse: warehouse.IsSystemWarehouse,
	}

	// Update in repository
	err = w.repo.Update(ctx, warehouseEntity)
	if err != nil {
		return utils.InternalErrorf("Failed to update warehouse", err, nil)
	}

	return nil
}
