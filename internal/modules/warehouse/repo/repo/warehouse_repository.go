package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/repo/pg"
)

type warehouseRepository struct {
	pgRepo pg.WarehousePostgreRepo
}

// CountByProp implements model.WarehouseRepository.
func (w *warehouseRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return w.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.WarehouseRepository.
func (w *warehouseRepository) Create(ctx context.Context, warehouse model.Warehouse) error {
	return w.pgRepo.Create(ctx, warehouse)
}

// Delete implements model.WarehouseRepository.
func (w *warehouseRepository) Delete(ctx context.Context, id string) error {
	return w.pgRepo.Delete(ctx, id)
}

// GetAll implements model.WarehouseRepository.
func (w *warehouseRepository) GetAll(ctx context.Context) ([]model.Warehouse, error) {
	return w.pgRepo.GetAll(ctx)
}

// GetByProp implements model.WarehouseRepository.
func (w *warehouseRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Warehouse, error) {
	return w.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.WarehouseRepository.
func (w *warehouseRepository) Update(ctx context.Context, warehouse model.Warehouse) error {
	return w.pgRepo.Update(ctx, warehouse)
}

func NewWarehouseRepository(pgRepo pg.WarehousePostgreRepo) model.WarehouseRepository {
	return &warehouseRepository{
		pgRepo: pgRepo,
	}
}
