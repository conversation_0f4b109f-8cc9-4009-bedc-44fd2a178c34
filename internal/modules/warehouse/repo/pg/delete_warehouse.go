package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *warehousePostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE warehouses 
			SET deleted_at = CURRENT_TIMESTAMP 
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete warehouse", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.WarehouseNotFoundf("Warehouse not found", nil, nil)
		}

		return nil
	})
}
