package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *warehousePostgreRepo) Update(ctx context.Context, warehouse model.Warehouse) error {
	return pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE warehouses 
			SET name = $2, code = $3, type = $4, category = $5, description = $6, address = $7, is_active = $8, is_system_warehouse = $9
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			warehouse.ID,
			warehouse.Name,
			warehouse.Code,
			warehouse.Type,
			warehouse.Category,
			warehouse.Description,
			warehouse.Address,
			warehouse.IsActive,
			warehouse.IsSystemWarehouse,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update warehouse", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.WarehouseNotFoundf("Warehouse not found", nil, nil)
		}

		return nil
	})
}
