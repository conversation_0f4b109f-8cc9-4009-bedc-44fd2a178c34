CREATE TABLE dev.warehouses (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    code VA<PERSON>HAR(255) NOT NULL UNIQUE,
    type VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_system_warehouse BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_warehouses_name ON dev.warehouses(name);
CREATE INDEX idx_warehouses_code ON dev.warehouses(code);
CREATE INDEX idx_warehouses_type ON dev.warehouses(type);
CREATE INDEX idx_warehouses_category ON dev.warehouses(category);
CREATE INDEX idx_warehouses_is_active ON dev.warehouses(is_active);
CREATE INDEX idx_warehouses_is_system_warehouse ON dev.warehouses(is_system_warehouse);
CREATE INDEX idx_warehouses_deleted_at ON dev.warehouses(deleted_at);
CREATE INDEX idx_warehouses_created_at ON dev.warehouses(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE TRIGGER update_warehouses_updated_at BEFORE UPDATE ON dev.warehouses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
