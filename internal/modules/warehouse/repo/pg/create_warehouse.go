package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *warehousePostgreRepo) Create(ctx context.Context, warehouse model.Warehouse) error {
	return pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO warehouses (id, name, code, type, category, description, address, is_active, is_system_warehouse)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		`

		_, err := conn.Exec(ctx, query,
			warehouse.ID,
			warehouse.Name,
			warehouse.Code,
			warehouse.Type,
			warehouse.Category,
			warehouse.Description,
			warehouse.Address,
			warehouse.IsActive,
			warehouse.IsSystemWarehouse,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create warehouse", err, nil)
		}

		return nil
	})
}
