package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WarehousePostgreRepo interface {
	Create(ctx context.Context, warehouse model.Warehouse) error
	Update(ctx context.Context, warehouse model.Warehouse) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Warehouse, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Warehouse, error)
	Delete(ctx context.Context, id string) error
}

type warehousePostgreRepo struct {
	pool *pgxpool.Pool
}

func NewWarehousePostgreRepo(pool *pgxpool.Pool) WarehousePostgreRepo {
	return &warehousePostgreRepo{
		pool: pool,
	}
}
