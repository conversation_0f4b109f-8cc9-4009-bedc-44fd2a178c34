package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *warehousePostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Warehouse, error) {
	var warehouse model.Warehouse
	
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, name, code, type, category, description, address, is_active, is_system_warehouse, created_at, updated_at, deleted_at
			FROM warehouses 
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		
		err := row.Scan(
			&warehouse.ID,
			&warehouse.Name,
			&warehouse.Code,
			&warehouse.Type,
			&warehouse.Category,
			&warehouse.Description,
			&warehouse.Address,
			&warehouse.IsActive,
			&warehouse.IsSystemWarehouse,
			&warehouse.CreatedAt,
			&warehouse.UpdatedAt,
			&warehouse.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.WarehouseNotFoundf("Warehouse not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get warehouse", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &warehouse, nil
}

func (w *warehousePostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT COUNT(*) 
			FROM warehouses 
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count warehouses", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (w *warehousePostgreRepo) GetAll(ctx context.Context) ([]model.Warehouse, error) {
	var warehouses []model.Warehouse
	
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, type, category, description, address, is_active, is_system_warehouse, created_at, updated_at, deleted_at
			FROM warehouses 
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get warehouses", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var warehouse model.Warehouse
			err := rows.Scan(
				&warehouse.ID,
				&warehouse.Name,
				&warehouse.Code,
				&warehouse.Type,
				&warehouse.Category,
				&warehouse.Description,
				&warehouse.Address,
				&warehouse.IsActive,
				&warehouse.IsSystemWarehouse,
				&warehouse.CreatedAt,
				&warehouse.UpdatedAt,
				&warehouse.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan warehouse", err, nil)
			}
			warehouses = append(warehouses, warehouse)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return warehouses, nil
}
