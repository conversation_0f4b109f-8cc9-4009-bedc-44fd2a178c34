package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	WarehouseConflictCode utils.ErrCode = utils.WarehouseCode + iota
	WarehouseConflictNameCode
	WarehouseConflictCodeCode
	WarehouseNotFoundCode
)

func WarehouseConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(WarehouseConflictCode, message, err, details)
}

func WarehouseConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(WarehouseConflictNameCode, message, err, details)
}

func WarehouseConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(WarehouseConflictCodeCode, message, err, details)
}

func WarehouseNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(WarehouseNotFoundCode, message, err, details)
}
