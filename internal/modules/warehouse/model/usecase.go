package model

import "context"

type WarehouseUsecase interface {
	Create(ctx context.Context, warehouse WarehouseCreate) (string, error)
	Update(ctx context.Context, warehouse WarehouseUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Warehouse, error)
	GetAll(ctx context.Context) ([]Warehouse, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	ValidateName(ctx context.Context, name string) error
}
