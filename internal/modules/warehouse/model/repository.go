package model

import "context"

type WarehouseRepository interface {
	Create(ctx context.Context, warehouse Warehouse) error
	Update(ctx context.Context, warehouse Warehouse) error
	GetByProp(ctx context.Context, prop string, value string) (*Warehouse, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Warehouse, error)
	Delete(ctx context.Context, id string) error
}
