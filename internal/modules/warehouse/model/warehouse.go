package model

import "time"

type Warehouse struct {
	ID                string
	Name              string
	Code              string
	Type              string
	Category          string
	Description       *string
	Address           *string
	IsActive          bool
	IsSystemWarehouse bool
	CreatedAt         *time.Time
	UpdatedAt         *time.Time
	DeletedAt         *time.Time
}

type WarehouseCreate struct {
	Name              string
	Code              string
	Type              string
	Category          string
	Description       *string
	Address           *string
	IsActive          bool
	IsSystemWarehouse bool
}

type WarehouseUpdate struct {
	ID                string
	Name              string
	Code              string
	Type              string
	Category          string
	Description       *string
	Address           *string
	IsActive          bool
	IsSystemWarehouse bool
}
