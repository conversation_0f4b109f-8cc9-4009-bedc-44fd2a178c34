package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
)

type recipeUsecase struct {
	repo model.RecipeRepository
}

// Delete implements model.RecipeUsecase.
func (r *recipeUsecase) Delete(ctx context.Context, id string) error {
	return r.repo.Delete(ctx, id)
}

// GetAll implements model.RecipeUsecase.
func (r *recipeUsecase) GetAll(ctx context.Context) ([]model.Recipe, error) {
	return r.repo.GetAll(ctx)
}

// GetByProp implements model.RecipeUsecase.
func (r *recipeUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Recipe, error) {
	return r.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.RecipeUsecase.
func (r *recipeUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := r.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.RecipeConflictCodef("Recipe code already exists", nil, nil)
	}

	return nil
}

// ValidateName implements model.RecipeUsecase.
func (r *recipeUsecase) ValidateName(ctx context.Context, name string) error {
	count, err := r.repo.CountByProp(ctx, "name", name)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.RecipeConflictNamef("Recipe name already exists", nil, nil)
	}

	return nil
}

func NewRecipeUsecase(repo model.RecipeRepository) model.RecipeUsecase {
	return &recipeUsecase{
		repo: repo,
	}
}
