package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.RecipeUsecase.
func (r *recipeUsecase) Create(ctx context.Context, recipe model.RecipeCreate) (string, error) {
	// Check if name already exists
	nameExists, err := r.repo.CountByProp(ctx, "name", recipe.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if recipe name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.RecipeConflictNamef("Recipe name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := r.repo.CountByProp(ctx, "code", recipe.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if recipe code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.RecipeConflictCodef("Recipe code already exists", nil, nil)
	}

	// Generate ID and create recipe
	id := ulid.Make().String()
	recipeEntity := model.Recipe{
		ID:                id,
		Name:              recipe.Name,
		Code:              recipe.Code,
		Type:              recipe.Type,
		BatchSize:         recipe.BatchSize,
		MeasurementUnitID: recipe.MeasurementUnitID,
	}

	err = r.repo.Create(ctx, recipeEntity)
	if err != nil {
		return "", err
	}

	// Create recipe products relationships
	if len(recipe.ProductIDs) > 0 {
		err = r.repo.CreateRecipeProducts(ctx, id, recipe.ProductIDs)
		if err != nil {
			return "", utils.InternalErrorf("Failed to create recipe products relationships", err, nil)
		}
	}

	// Create recipe components relationships
	if len(recipe.Components) > 0 {
		err = r.repo.CreateRecipeComponents(ctx, id, recipe.Components)
		if err != nil {
			return "", utils.InternalErrorf("Failed to create recipe components relationships", err, nil)
		}
	}

	return id, nil
}
