package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	RecipeConflictCode     utils.ErrCode = utils.RecipeCode + iota
	RecipeConflictNameCode
	RecipeConflictCodeCode
	RecipeNotFoundCode
)

func RecipeConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(RecipeConflictCode, message, err, details)
}

func RecipeConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(RecipeConflictNameCode, message, err, details)
}

func RecipeConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(RecipeConflictCodeCode, message, err, details)
}

func RecipeNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(RecipeNotFoundCode, message, err, details)
}
