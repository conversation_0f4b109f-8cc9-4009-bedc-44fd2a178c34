package model

import "context"

type RecipeUsecase interface {
	Create(ctx context.Context, recipe RecipeCreate) (string, error)
	Update(ctx context.Context, recipe RecipeUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Recipe, error)
	GetAll(ctx context.Context) ([]Recipe, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	ValidateName(ctx context.Context, name string) error
}
