package model

import (
	"time"

	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
)

type Recipe struct {
	ID                string
	Name              string
	Code              string
	Type              string
	BatchSize         float64
	MeasurementUnitID *string
	Products          []productModel.Product
	Components        []RecipeComponent
	CreatedAt         *time.Time
	UpdatedAt         *time.Time
	DeletedAt         *time.Time
}

type RecipeComponent struct {
	Product           productModel.Product
	ProductCategoryID string // Main category (parent_id is null)
	Quantity          float64
}

type RecipeCreate struct {
	Name              string
	Code              string
	Type              string
	BatchSize         float64
	MeasurementUnitID *string
	ProductIDs        []string
	Components        []RecipeComponentCreate
}

type RecipeUpdate struct {
	ID                string
	Name              string
	Code              string
	Type              string
	BatchSize         float64
	MeasurementUnitID *string
	ProductIDs        []string
	Components        []RecipeComponentCreate
}

type RecipeComponentCreate struct {
	ProductID string
	Quantity  float64
}
