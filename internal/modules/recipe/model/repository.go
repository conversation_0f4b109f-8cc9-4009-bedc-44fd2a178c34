package model

import "context"

type RecipeRepository interface {
	Create(ctx context.Context, recipe Recipe) error
	Update(ctx context.Context, recipe Recipe) error
	GetByProp(ctx context.Context, prop string, value string) (*Recipe, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Recipe, error)
	Delete(ctx context.Context, id string) error
	CreateRecipeComponents(ctx context.Context, recipeID string, components []RecipeComponentCreate) error
	UpdateRecipeComponents(ctx context.Context, recipeID string, components []RecipeComponentCreate) error
	GetRecipeComponents(ctx context.Context, recipeID string) ([]RecipeComponent, error)
	DeleteRecipeComponents(ctx context.Context, recipeID string) error
	CreateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error
	UpdateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error
	GetRecipeProducts(ctx context.Context, recipeID string) ([]string, error)
	DeleteRecipeProducts(ctx context.Context, recipeID string) error
}
