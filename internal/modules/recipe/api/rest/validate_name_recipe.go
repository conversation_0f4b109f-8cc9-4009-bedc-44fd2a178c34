package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateName implements RecipeHandler.
func (r *recipeHandler) ValidateName(w http.ResponseWriter, req *http.Request) {
	name := req.PathValue("name")
	ctx := req.Context()

	if name == "" {
		rest.ErrorResponse(w, req, utils.BadRequestf("Name parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := r.useCase.ValidateName(ctx, name)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Name validation failed")
		return
	}

	rest.SuccessResponse(w, req, http.StatusOK)
}
