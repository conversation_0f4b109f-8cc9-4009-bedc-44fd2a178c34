package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements RecipeHandler.
func (r *recipeHandler) ValidateCode(w http.ResponseWriter, req *http.Request) {
	code := req.PathValue("code")
	ctx := req.Context()

	if code == "" {
		rest.ErrorResponse(w, req, utils.BadRequestf("Code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := r.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Code validation failed")
		return
	}

	rest.SuccessResponse(w, req, http.StatusOK)
}
