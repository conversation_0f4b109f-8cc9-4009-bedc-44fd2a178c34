package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements RecipeHandler.
func (r *recipeHandler) Delete(w http.ResponseWriter, req *http.Request) {
	id := req.PathValue("id")
	ctx := req.Context()

	err := r.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Failed to delete recipe")
		return
	}

	rest.SuccessResponse(w, req, http.StatusNoContent)
}
