package rest

import (
	"time"

	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
)

type recipeResult struct {
	ID                string                  `json:"id"`
	Name              string                  `json:"name"`
	Code              string                  `json:"code"`
	Type              string                  `json:"type"`
	BatchSize         float64                 `json:"batch_size"`
	MeasurementUnitID *string                 `json:"measurement_unit_id"`
	Products          []productResult         `json:"products"`
	Components        []recipeComponentResult `json:"components"`
	CreatedAt         *time.Time              `json:"created_at"`
	UpdatedAt         *time.Time              `json:"updated_at"`
	DeletedAt         *time.Time              `json:"deleted_at"`
}

type productResult struct {
	ID                string     `json:"id"`
	Name              string     `json:"name"`
	ImageURL          *string    `json:"image_url"`
	CommercialName    string     `json:"commercial_name"`
	Code              string     `json:"code"`
	SKUCode           string     `json:"sku_code"`
	MeasurementUnitID string     `json:"measurement_unit_id"`
	CategoryIDs       []string   `json:"category_ids"`
	BrandID           string     `json:"brand_id"`
	State             string     `json:"state"`
	Description       *string    `json:"description"`
	CanBeSold         bool       `json:"can_be_sold"`
	CanBePurchased    bool       `json:"can_be_purchased"`
	CostPrice         *float64   `json:"cost_price"`
	CostPriceTotal    int        `json:"cost_price_total"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
	DeletedAt         *time.Time `json:"deleted_at"`
}

type recipeComponentResult struct {
	Product           productResult `json:"product"`
	ProductCategoryID string        `json:"product_category_id"`
	Quantity          float64       `json:"quantity"`
}

func productToResult(product *productModel.Product) productResult {
	return productResult{
		ID:                product.ID,
		Name:              product.Name,
		ImageURL:          product.ImageURL,
		CommercialName:    product.CommercialName,
		Code:              product.Code,
		SKUCode:           product.SKUCode,
		MeasurementUnitID: product.MeasurementUnitID,
		CategoryIDs:       product.CategoryIDs,
		BrandID:           product.BrandID,
		State:             product.State,
		Description:       product.Description,
		CanBeSold:         product.CanBeSold,
		CanBePurchased:    product.CanBePurchased,
		CostPrice:         product.CostPrice,
		CostPriceTotal:    product.CostPriceTotal,
		CreatedAt:         product.CreatedAt,
		UpdatedAt:         product.UpdatedAt,
		DeletedAt:         product.DeletedAt,
	}
}

func recipeToResult(recipe *model.Recipe) recipeResult {
	products := make([]productResult, len(recipe.Products))
	for i, product := range recipe.Products {
		products[i] = productToResult(&product)
	}

	components := make([]recipeComponentResult, len(recipe.Components))
	for i, component := range recipe.Components {
		components[i] = recipeComponentResult{
			Product:           productToResult(&component.Product),
			ProductCategoryID: component.ProductCategoryID,
			Quantity:          component.Quantity,
		}
	}

	return recipeResult{
		ID:                recipe.ID,
		Name:              recipe.Name,
		Code:              recipe.Code,
		Type:              recipe.Type,
		BatchSize:         recipe.BatchSize,
		MeasurementUnitID: recipe.MeasurementUnitID,
		Products:          products,
		Components:        components,
		CreatedAt:         recipe.CreatedAt,
		UpdatedAt:         recipe.UpdatedAt,
		DeletedAt:         recipe.DeletedAt,
	}
}

type recipeCreate struct {
	Name              string                  `json:"name" validate:"required"`
	Code              string                  `json:"code" validate:"required"`
	Type              string                  `json:"type" validate:"required"`
	BatchSize         float64                 `json:"batch_size" validate:"required,gt=0"`
	MeasurementUnitID *string                 `json:"measurement_unit_id" validate:"required"`
	ProductIDs        []string                `json:"product_ids"`
	Components        []recipeComponentCreate `json:"components"`
}

type recipeComponentCreate struct {
	ProductID string  `json:"product_id" validate:"required"`
	Quantity  float64 `json:"quantity" validate:"required,gt=0"`
}

func recipeCreateToModel(req recipeCreate) model.RecipeCreate {
	components := make([]model.RecipeComponentCreate, len(req.Components))
	for i, component := range req.Components {
		components[i] = model.RecipeComponentCreate{
			ProductID: component.ProductID,
			Quantity:  component.Quantity,
		}
	}

	return model.RecipeCreate{
		Name:              req.Name,
		Code:              req.Code,
		Type:              req.Type,
		BatchSize:         req.BatchSize,
		MeasurementUnitID: req.MeasurementUnitID,
		ProductIDs:        req.ProductIDs,
		Components:        components,
	}
}

type recipeUpdate struct {
	ID                string                  `json:"id" validate:"required"`
	Name              string                  `json:"name" validate:"required"`
	Code              string                  `json:"code" validate:"required"`
	Type              string                  `json:"type" validate:"required"`
	BatchSize         float64                 `json:"batch_size" validate:"required,gt=0"`
	MeasurementUnitID *string                 `json:"measurement_unit_id" validate:"required"`
	ProductIDs        []string                `json:"product_ids"`
	Components        []recipeComponentCreate `json:"components"`
}

func recipeUpdateToModel(req recipeUpdate) model.RecipeUpdate {
	components := make([]model.RecipeComponentCreate, len(req.Components))
	for i, component := range req.Components {
		components[i] = model.RecipeComponentCreate{
			ProductID: component.ProductID,
			Quantity:  component.Quantity,
		}
	}

	return model.RecipeUpdate{
		ID:                req.ID,
		Name:              req.Name,
		Code:              req.Code,
		Type:              req.Type,
		BatchSize:         req.BatchSize,
		MeasurementUnitID: req.MeasurementUnitID,
		ProductIDs:        req.ProductIDs,
		Components:        components,
	}
}
