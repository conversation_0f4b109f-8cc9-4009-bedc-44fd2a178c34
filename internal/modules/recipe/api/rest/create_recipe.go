package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements RecipeHandler.
func (r *recipeHandler) Create(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()

	request, err := rest.DecodeAndValidate[recipeCreate](w, req, r.validator)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		return
	}

	id, err := r.useCase.Create(ctx, recipeCreateToModel(*request))
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Failed to create recipe")
		return
	}

	rest.SuccessDResponse(w, req, id, http.StatusCreated)
}
