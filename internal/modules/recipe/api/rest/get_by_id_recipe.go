package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements RecipeHandler.
func (r *recipeHandler) GetById(w http.ResponseWriter, req *http.Request) {
	id := req.PathValue("id")
	ctx := req.Context()

	recipe, err := r.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Failed to get recipe by id")
		return
	}

	rest.SuccessDResponse(w, req, recipeToResult(recipe), http.StatusOK)
}
