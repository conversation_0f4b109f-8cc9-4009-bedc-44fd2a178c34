package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type RecipeHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	ValidateName(w http.ResponseWriter, r *http.Request)
}

type recipeHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.RecipeUsecase
}

func NewRecipeHandler(log *logrus.Logger, validator *validator.Validate, useCase model.RecipeUsecase) RecipeHandler {
	return &recipeHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
