package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.RecipeConflictCode:     http.StatusConflict,
	model.RecipeConflictNameCode: http.StatusConflict,
	model.RecipeConflictCodeCode: http.StatusConflict,
	model.RecipeNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
