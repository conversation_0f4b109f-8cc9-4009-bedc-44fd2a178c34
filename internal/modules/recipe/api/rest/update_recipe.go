package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements RecipeHandler.
func (r *recipeHandler) Update(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()

	request, err := rest.DecodeAndValidate[recipeUpdate](w, req, r.validator)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		return
	}

	if err := r.useCase.Update(ctx, recipeUpdateToModel(*request)); err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Failed to update recipe")
		return
	}

	rest.SuccessResponse(w, req, http.StatusNoContent)
}
