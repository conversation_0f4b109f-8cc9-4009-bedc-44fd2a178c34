package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements RecipeHandler.
func (r *recipeHandler) GetAll(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()

	recipes, err := r.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, r.log, err)
		respErrHandler(w, req, err, "Failed to get all recipes")
		return
	}

	results := make([]recipeResult, len(recipes))
	for i, recipe := range recipes {
		results[i] = recipeToResult(&recipe)
	}

	rest.SuccessDResponse(w, req, results, http.StatusOK)
}
