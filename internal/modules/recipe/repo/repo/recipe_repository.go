package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/repo/pg"
)

type recipeRepository struct {
	pgRepo pg.RecipePostgreRepo
}

// CountByProp implements model.RecipeRepository.
func (r *recipeRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return r.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.RecipeRepository.
func (r *recipeRepository) Create(ctx context.Context, recipe model.Recipe) error {
	return r.pgRepo.Create(ctx, recipe)
}

// Delete implements model.RecipeRepository.
func (r *recipeRepository) Delete(ctx context.Context, id string) error {
	return r.pgRepo.Delete(ctx, id)
}

// GetAll implements model.RecipeRepository.
func (r *recipeRepository) GetAll(ctx context.Context) ([]model.Recipe, error) {
	return r.pgRepo.GetAll(ctx)
}

// GetByProp implements model.RecipeRepository.
func (r *recipeRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Recipe, error) {
	return r.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.RecipeRepository.
func (r *recipeRepository) Update(ctx context.Context, recipe model.Recipe) error {
	return r.pgRepo.Update(ctx, recipe)
}

// CreateRecipeComponents implements model.RecipeRepository.
func (r *recipeRepository) CreateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error {
	return r.pgRepo.CreateRecipeComponents(ctx, recipeID, components)
}

// UpdateRecipeComponents implements model.RecipeRepository.
func (r *recipeRepository) UpdateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error {
	return r.pgRepo.UpdateRecipeComponents(ctx, recipeID, components)
}

// GetRecipeComponents implements model.RecipeRepository.
func (r *recipeRepository) GetRecipeComponents(ctx context.Context, recipeID string) ([]model.RecipeComponent, error) {
	return r.pgRepo.GetRecipeComponents(ctx, recipeID)
}

// DeleteRecipeComponents implements model.RecipeRepository.
func (r *recipeRepository) DeleteRecipeComponents(ctx context.Context, recipeID string) error {
	return r.pgRepo.DeleteRecipeComponents(ctx, recipeID)
}

// CreateRecipeProducts implements model.RecipeRepository.
func (r *recipeRepository) CreateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error {
	return r.pgRepo.CreateRecipeProducts(ctx, recipeID, productIDs)
}

// UpdateRecipeProducts implements model.RecipeRepository.
func (r *recipeRepository) UpdateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error {
	return r.pgRepo.UpdateRecipeProducts(ctx, recipeID, productIDs)
}

// GetRecipeProducts implements model.RecipeRepository.
func (r *recipeRepository) GetRecipeProducts(ctx context.Context, recipeID string) ([]string, error) {
	return r.pgRepo.GetRecipeProducts(ctx, recipeID)
}

// DeleteRecipeProducts implements model.RecipeRepository.
func (r *recipeRepository) DeleteRecipeProducts(ctx context.Context, recipeID string) error {
	return r.pgRepo.DeleteRecipeProducts(ctx, recipeID)
}

func NewRecipeRepository(pgRepo pg.RecipePostgreRepo) model.RecipeRepository {
	return &recipeRepository{
		pgRepo: pgRepo,
	}
}
