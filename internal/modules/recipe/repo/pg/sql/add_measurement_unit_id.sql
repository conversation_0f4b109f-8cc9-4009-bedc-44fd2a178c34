-- Migration to add measurement_unit_id column to recipes table
-- This column will store the measurement unit reference for the recipe

-- Step 1: Add the measurement_unit_id column to recipes table
ALTER TABLE dev.recipes 
ADD COLUMN IF NOT EXISTS measurement_unit_id VARCHAR(255);

-- Step 2: Add foreign key constraint to measurement_units table
-- Note: PostgreSQL doesn't support IF NOT EXISTS with ADD CONSTRAINT
-- Use DO block to check if constraint exists before adding
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_recipes_measurement_unit_id' 
        AND table_schema = 'dev' 
        AND table_name = 'recipes'
    ) THEN
        ALTER TABLE dev.recipes 
        ADD CONSTRAINT fk_recipes_measurement_unit_id 
        FOREIGN KEY (measurement_unit_id) REFERENCES dev.measurement_units(id);
    END IF;
END $$;

-- Step 3: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_recipes_measurement_unit_id ON dev.recipes(measurement_unit_id);

-- Step 4: Add comment to document the column purpose
COMMENT ON COLUMN dev.recipes.measurement_unit_id IS 'Foreign key reference to measurement_units table - the measurement unit for this recipe';
