CREATE TABLE dev.recipes (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    code VARCHAR(255) NOT NULL UNIQUE,
    type VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Junction table for many-to-many relationship between recipes and products
CREATE TABLE dev.recipe_products (
    id VARCHAR(255) PRIMARY KEY,
    recipe_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (recipe_id) REFERENCES dev.recipes(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES dev.products(id) ON DELETE CASCADE,
    UNIQUE(recipe_id, product_id)
);

-- Table for recipe components
-- Note: category_id is not stored here, it will be fetched from products table via business logic
CREATE TABLE dev.recipe_components (
    id VARCHAR(255) PRIMARY KEY,
    recipe_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (recipe_id) REFERENCES dev.recipes(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES dev.products(id) ON DELETE CASCADE,
    UNIQUE(recipe_id, product_id)
);
