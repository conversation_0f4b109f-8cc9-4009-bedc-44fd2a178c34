package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type RecipePostgreRepo interface {
	Create(ctx context.Context, recipe model.Recipe) error
	Update(ctx context.Context, recipe model.Recipe) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Recipe, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Recipe, error)
	Delete(ctx context.Context, id string) error
	CreateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error
	UpdateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error
	GetRecipeComponents(ctx context.Context, recipeID string) ([]model.RecipeComponent, error)
	DeleteRecipeComponents(ctx context.Context, recipeID string) error
	CreateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error
	UpdateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error
	GetRecipeProducts(ctx context.Context, recipeID string) ([]string, error)
	DeleteRecipeProducts(ctx context.Context, recipeID string) error
}

type recipePostgreRepo struct {
	pool *pgxpool.Pool
}

func NewRecipePostgreRepo(pool *pgxpool.Pool) RecipePostgreRepo {
	return &recipePostgreRepo{
		pool: pool,
	}
}
