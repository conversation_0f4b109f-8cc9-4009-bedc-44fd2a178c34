package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (r *recipePostgreRepo) CreateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		for _, productID := range productIDs {
			id := ulid.Make().String()
			query := `
				INSERT INTO recipe_products (id, recipe_id, product_id)
				VALUES ($1, $2, $3)
				ON CONFLICT (recipe_id, product_id) DO NOTHING
			`
			_, err := conn.Exec(ctx, query, id, recipeID, productID)
			if err != nil {
				return utils.InternalErrorf("failed to create recipe product relationship", err, nil)
			}
		}
		return nil
	})
}

func (r *recipePostgreRepo) UpdateRecipeProducts(ctx context.Context, recipeID string, productIDs []string) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// First, delete existing relationships
		deleteQuery := `DELETE FROM recipe_products WHERE recipe_id = $1`
		_, err := conn.Exec(ctx, deleteQuery, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing recipe products", err, nil)
		}

		// Then, create new relationships
		for _, productID := range productIDs {
			id := ulid.Make().String()
			insertQuery := `
				INSERT INTO recipe_products (id, recipe_id, product_id)
				VALUES ($1, $2, $3)
			`
			_, err := conn.Exec(ctx, insertQuery, id, recipeID, productID)
			if err != nil {
				return utils.InternalErrorf("failed to create recipe product relationship", err, nil)
			}
		}
		return nil
	})
}

func (r *recipePostgreRepo) GetRecipeProducts(ctx context.Context, recipeID string) ([]string, error) {
	var productIDs []string
	
	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT product_id
			FROM recipe_products
			WHERE recipe_id = $1
			ORDER BY created_at ASC
		`
		
		rows, err := conn.Query(ctx, query, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to get recipe products", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var productID string
			err := rows.Scan(&productID)
			if err != nil {
				return utils.InternalErrorf("failed to scan product ID", err, nil)
			}
			productIDs = append(productIDs, productID)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate recipe products", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return productIDs, nil
}

func (r *recipePostgreRepo) DeleteRecipeProducts(ctx context.Context, recipeID string) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `DELETE FROM recipe_products WHERE recipe_id = $1`
		_, err := conn.Exec(ctx, query, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to delete recipe products", err, nil)
		}
		return nil
	})
}
