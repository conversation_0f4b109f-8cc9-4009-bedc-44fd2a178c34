package pg

import (
	"context"

	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/oklog/ulid/v2"
)

func (r *recipePostgreRepo) CreateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		for _, component := range components {
			id := ulid.Make().String()
			query := `
				INSERT INTO recipe_components (id, recipe_id, product_id, quantity)
				VALUES ($1, $2, $3, $4)
				ON CONFLICT (recipe_id, product_id) DO UPDATE SET
					quantity = EXCLUDED.quantity
			`
			_, err := conn.Exec(ctx, query, id, recipeID, component.ProductID, component.Quantity)
			if err != nil {
				return utils.InternalErrorf("failed to create recipe component relationship", err, nil)
			}
		}
		return nil
	})
}

func (r *recipePostgreRepo) UpdateRecipeComponents(ctx context.Context, recipeID string, components []model.RecipeComponentCreate) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// First, delete existing relationships
		deleteQuery := `DELETE FROM recipe_components WHERE recipe_id = $1`
		_, err := conn.Exec(ctx, deleteQuery, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to delete existing recipe components", err, nil)
		}

		// Then, create new relationships
		for _, component := range components {
			id := ulid.Make().String()
			insertQuery := `
				INSERT INTO recipe_components (id, recipe_id, product_id, quantity)
				VALUES ($1, $2, $3, $4)
			`
			_, err := conn.Exec(ctx, insertQuery, id, recipeID, component.ProductID, component.Quantity)
			if err != nil {
				return utils.InternalErrorf("failed to create recipe component relationship", err, nil)
			}
		}
		return nil
	})
}

func (r *recipePostgreRepo) GetRecipeComponents(ctx context.Context, recipeID string) ([]model.RecipeComponent, error) {
	var components []model.RecipeComponent

	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.brand_id, p.state,
				p.description, p.can_be_sold, p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids,
				rc.quantity,
				COALESCE(
					(SELECT c.id
					 FROM product_categories pc2
					 JOIN categories c ON pc2.category_id = c.id
					 WHERE pc2.product_id = p.id AND c.category_id IS NULL
					 LIMIT 1),
					''
				) as main_category_id
			FROM recipe_components rc
			JOIN products p ON rc.product_id = p.id
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE rc.recipe_id = $1 AND p.deleted_at IS NULL
			GROUP BY p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
					 p.measurement_unit_id, p.brand_id, p.state,
					 p.description, p.can_be_sold, p.can_be_purchased, p.cost_price, p.cost_price_total,
					 p.created_at, p.updated_at, p.deleted_at, rc.quantity, rc.created_at
			ORDER BY rc.created_at ASC
		`

		rows, err := conn.Query(ctx, query, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to get recipe components", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var component model.RecipeComponent
			var product productModel.Product
			var categoryIDs []string

			err := rows.Scan(
				&product.ID,
				&product.Name,
				&product.ImageURL,
				&product.CommercialName,
				&product.Code,
				&product.SKUCode,
				&product.MeasurementUnitID,
				&product.BrandID,
				&product.State,
				&product.Description,
				&product.CanBeSold,
				&product.CanBePurchased,
				&product.CostPrice,
				&product.CostPriceTotal,
				&product.CreatedAt,
				&product.UpdatedAt,
				&product.DeletedAt,
				&categoryIDs,
				&component.Quantity,
				&component.ProductCategoryID,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan recipe component", err, nil)
			}

			product.CategoryIDs = categoryIDs
			component.Product = product
			components = append(components, component)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate recipe components", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return components, nil
}

func (r *recipePostgreRepo) DeleteRecipeComponents(ctx context.Context, recipeID string) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `DELETE FROM recipe_components WHERE recipe_id = $1`
		_, err := conn.Exec(ctx, query, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to delete recipe components", err, nil)
		}
		return nil
	})
}
