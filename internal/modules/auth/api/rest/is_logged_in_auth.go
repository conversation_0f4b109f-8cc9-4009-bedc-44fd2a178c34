package rest

import (
	"fmt"
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// IsLoggedIn implements AuthHandler.
func (a *authHandler) IsLoggedIn(w http.ResponseWriter, r *http.Request) {
	userID, ok := r.Context().Value(utils.UserIDKey).(string)
	if !ok {
		utils.LogErr(r.Context(), a.log, fmt.Errorf("failed to get user id"))
		respErrHandler(w, r, model.NotLoggedInf("failed to get user id", nil, nil), "Failed to get user id")
		return
	}

	session, err := a.useCase.GetSessionByID(r.Context(), userID)
	if err != nil {
		utils.LogErr(r.Context(), a.log, err)
		respErrHandler(w, r, err, "Failed to get session")
		return
	}

	rest.SuccessDResponse(w, r, sessionModelToResult(*session), http.StatusOK)
}
