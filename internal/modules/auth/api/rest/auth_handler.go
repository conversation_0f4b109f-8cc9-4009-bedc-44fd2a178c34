package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type AuthHandler interface {
	Login(w http.ResponseWriter, r *http.Request)
	Logout(w http.ResponseWriter, r *http.Request)
	IsLoggedIn(w http.ResponseWriter, r *http.Request)
}

type authHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.AuthUseCase
}

func NewAuthHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.AuthUseCase,
) AuthHandler {
	return &authHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
