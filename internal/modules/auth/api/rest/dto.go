package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
)

type login struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

func loginToModel(login login) model.Login {
	return model.Login{
		Username: login.Username,
		Password: login.Password,
	}
}

type userResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Email     string     `json:"email"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

type sessionResult struct {
	User userResult `json:"user"`
}

func sessionModelToResult(session model.Session) sessionResult {
	return sessionResult{
		User: userResult{
			ID:        session.User.ID,
			Name:      session.User.Name,
			Email:     session.User.Email,
			CreatedAt: session.User.CreatedAt,
			UpdatedAt: session.User.UpdatedAt,
			DeletedAt: session.User.DeletedAt,
		},
	}
}
