package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Login implements AuthHandler.
func (a *authHandler) Login(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[login](w, r, a.validator)
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		return
	}

	session, err := a.useCase.Login(ctx, loginToModel(*req))
	if err != nil {
		utils.LogErr(ctx, a.log, err)
		respErrHandler(w, r, err, "Failed to login")
		return
	}

	http.SetCookie(w, &http.Cookie{
		Name:     utils.TokenKey,
		Value:    session.Token,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		Expires:  time.Now().Add(24 * 7 * time.Hour),
	})

	rest.SuccessDResponse(w, r, sessionModelToResult(*session), http.StatusOK)
}
