package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ErrInvalidCredentials: http.StatusBadRequest,
	model.ErrInvalidPassword:    http.StatusBadRequest,
	model.ErrNotLoggedIn:        http.StatusUnauthorized,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
