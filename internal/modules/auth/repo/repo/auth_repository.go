package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/repo/pg"
	userModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
)

type authRepository struct {
	pgRepo pg.AuthPostgreRepo
}

// Login implements model.AuthRepository.
func (a *authRepository) Login(ctx context.Context, login *model.Login) (*userModel.User, error) {
	return a.pgRepo.Login(ctx, login)
}

// GetSessionByID implements model.AuthRepository.
func (a *authRepository) GetSessionByID(ctx context.Context, userID string) (*userModel.User, error) {
	return a.pgRepo.GetSessionByID(ctx, userID)
}

func NewAuthRepository(pgRepo pg.AuthPostgreRepo) model.AuthRepository {
	return &authRepository{
		pgRepo: pgRepo,
	}
}
