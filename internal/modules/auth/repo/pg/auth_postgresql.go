package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	userModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type AuthPostgreRepo interface {
	Login(ctx context.Context, login *model.Login) (*userModel.User, error)
	GetSessionByID(ctx context.Context, userID string) (*userModel.User, error)
}

type authPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewAuthPostgreRepo(pool *pgxpool.Pool) AuthPostgreRepo {
	return &authPostgreRepo{
		pool: pool,
	}
}
