package pg

import (
	"context"
	"errors"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	userModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// Login implements AuthPostgreRepo.
func (a *authPostgreRepo) Login(ctx context.Context, login *model.Login) (*userModel.User, error) {
	var user userModel.User

	err := pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, email, hashed_pw, created_at, updated_at, deleted_at
			FROM users
			WHERE (email = $1 OR name = $1) AND deleted_at IS NULL
		`

		row := conn.QueryRow(ctx, query, login.Username)
		err := row.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.HashedPW,
			&user.CreatedAt,
			&user.UpdatedAt,
			&user.DeletedAt,
		)

		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return model.InvalidCredentialsf("invalid email or password", err, nil)
			}
			return utils.InternalErrorf("failed to get user credentials", err, nil)
		}

		// Compare password with hashed password
		if !utils.MatchPassword(user.HashedPW, login.Password) {
			return model.InvalidCredentialsf("invalid email or password", nil, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &user, nil
}
