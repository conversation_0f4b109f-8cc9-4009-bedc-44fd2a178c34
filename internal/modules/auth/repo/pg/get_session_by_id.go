package pg

import (
	"context"
	"errors"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	userModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// GetSessionByID implements AuthPostgreRepo.
func (a *authPostgreRepo) GetSessionByID(ctx context.Context, userID string) (*userModel.User, error) {
	var user userModel.User

	err := pg.ExecuteInSchema(ctx, a.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, name, email, hashed_pw, created_at, updated_at, deleted_at
            FROM users
            WHERE id = $1 AND deleted_at IS NULL
        `

		row := conn.QueryRow(ctx, query, userID)
		err := row.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.HashedPW,
			&user.CreatedAt,
			&user.UpdatedAt,
			&user.DeletedAt,
		)

		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return model.UserNotFoundf("user not found", err, nil)
			}
			return utils.InternalErrorf("failed to get user", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &user, nil
}
