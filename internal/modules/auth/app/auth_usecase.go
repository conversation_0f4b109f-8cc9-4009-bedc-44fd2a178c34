package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
)

type authUsecase struct {
	repo model.AuthRepository
}

// GetSessionByID implements model.AuthUseCase.
func (a *authUsecase) GetSessionByID(ctx context.Context, userID string) (*model.Session, error) {
	user, err := a.repo.GetSessionByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	session := model.Session{
		User: user,
	}

	return &session, nil
}

func NewAuthUsecase(repo model.AuthRepository) model.AuthUseCase {
	return &authUsecase{
		repo: repo,
	}
}
