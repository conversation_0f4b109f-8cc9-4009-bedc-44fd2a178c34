package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Login implements model.AuthUseCase.
func (a *authUsecase) Login(ctx context.Context, login model.Login) (*model.Session, error) {
	user, err := a.repo.Login(ctx, &login)
	if err != nil {
		return nil, err
	}

	// Generate token for the user
	token, err := utils.GenerateToken(user.ID)
	if err != nil {
		return nil, utils.InternalErrorf("failed to generate token", err, nil)
	}

	return &model.Session{
		Token: token,
		User:  user,
	}, nil
}
