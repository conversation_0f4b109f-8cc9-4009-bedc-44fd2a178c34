package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ErrInvalidCredentials utils.ErrCode = utils.AuthCode + iota
	ErrInvalidPassword
	ErrUserNotFound
	ErrNotLoggedIn
)

func InvalidCredentialsf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ErrInvalidCredentials, message, err, details)
}

func InvalidPasswordf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ErrInvalidPassword, message, err, details)
}

func UserNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ErrUserNotFound, message, err, details)
}

func NotLoggedInf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ErrNotLoggedIn, message, err, details)
}
