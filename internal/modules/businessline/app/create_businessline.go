package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.BusinessLineUsecase.
func (b *businessLineUsecase) Create(ctx context.Context, businessLine model.BusinessLineCreate) (string, error) {
	// Check if name already exists
	nameExists, err := b.repo.CountByProp(ctx, "name", businessLine.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if business line name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.BusinessLineConflictNamef("Business line name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := b.repo.CountByProp(ctx, "code", businessLine.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if business line code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.BusinessLineConflictCodef("Business line code already exists", nil, nil)
	}

	// Validate parent business line exists if provided
	if businessLine.ParentID != nil && *businessLine.ParentID != "" {
		_, err := b.repo.GetByProp(ctx, "id", *businessLine.ParentID)
		if err != nil {
			return "", model.BusinessLineInvalidParentf("Parent business line does not exist", err, nil)
		}
	}

	newBusinessLine := model.BusinessLine{
		ID:       utils.UniqueId(),
		Name:     businessLine.Name,
		Code:     businessLine.Code,
		ParentID: businessLine.ParentID,
	}

	err = b.repo.Create(ctx, newBusinessLine)
	if err != nil {
		return "", err
	}

	return newBusinessLine.ID, nil
}
