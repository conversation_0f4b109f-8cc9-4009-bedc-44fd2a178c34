package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
)

type businessLineUsecase struct {
	repo model.BusinessLineRepository
}

// Delete implements model.BusinessLineUsecase.
func (b *businessLineUsecase) Delete(ctx context.Context, id string) error {
	return b.repo.Delete(ctx, id)
}

// GetAll implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetAll(ctx context.Context) ([]model.BusinessLine, error) {
	return b.repo.GetAll(ctx)
}

// GetByProp implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.BusinessLine, error) {
	return b.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.BusinessLineUsecase.
func (b *businessLineUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := b.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.BusinessLineConflictCodef("Business line code already exists", nil, nil)
	}

	return nil
}

// GetSublines implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetSublines(ctx context.Context, businessLineID string) ([]model.BusinessLine, error) {
	return b.repo.GetSublines(ctx, businessLineID)
}

// GetSublinesByCode implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetSublinesByCode(ctx context.Context, code string) ([]model.BusinessLine, error) {
	// First get the business line by code
	businessLine, err := b.repo.GetByProp(ctx, "code", code)
	if err != nil {
		return nil, err
	}

	// Then get its sublines
	return b.repo.GetSublines(ctx, businessLine.ID)
}

// GetParentLines implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetParentLines(ctx context.Context) ([]model.BusinessLine, error) {
	return b.repo.GetParentLines(ctx)
}

// GetBusinessLineWithDetails implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetBusinessLineWithDetails(ctx context.Context, businessLineID string) (*model.BusinessLineWithSublines, error) {
	// Get the main business line
	businessLine, err := b.repo.GetByProp(ctx, "id", businessLineID)
	if err != nil {
		return nil, err
	}

	// Get sublines
	sublines, err := b.repo.GetSublines(ctx, businessLineID)
	if err != nil {
		return nil, err
	}

	// Get parent business line
	parent, err := b.repo.GetParentLine(ctx, businessLineID)
	if err != nil {
		return nil, err
	}

	return &model.BusinessLineWithSublines{
		BusinessLine: *businessLine,
		Sublines:     sublines,
		Parent:       parent,
	}, nil
}

// GetBusinessLineWithDetailsByCode implements model.BusinessLineUsecase.
func (b *businessLineUsecase) GetBusinessLineWithDetailsByCode(ctx context.Context, code string) (*model.BusinessLineWithSublines, error) {
	// First get the business line by code
	businessLine, err := b.repo.GetByProp(ctx, "code", code)
	if err != nil {
		return nil, err
	}

	// Then get its details
	return b.GetBusinessLineWithDetails(ctx, businessLine.ID)
}

func NewBusinessLineUsecase(repo model.BusinessLineRepository) model.BusinessLineUsecase {
	return &businessLineUsecase{
		repo: repo,
	}
}
