package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.BusinessLineUsecase.
func (b *businessLineUsecase) Update(ctx context.Context, businessLine model.BusinessLineUpdate) error {
	// Get the current business line to check if name/code has changed
	currentBusinessLine, err := b.repo.GetByProp(ctx, "id", businessLine.ID)
	if err != nil {
		return err
	}

	// Check if name has changed and if it's already in use
	if currentBusinessLine.Name != businessLine.Name {
		nameExists, err := b.repo.CountByProp(ctx, "name", businessLine.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if business line name exists", err, nil)
		}

		if nameExists > 0 {
			return model.BusinessLineConflictNamef("Business line name already exists", nil, nil)
		}
	}

	// Check if code has changed and if it's already in use
	if currentBusinessLine.Code != businessLine.Code {
		codeExists, err := b.repo.CountByProp(ctx, "code", businessLine.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if business line code exists", err, nil)
		}

		if codeExists > 0 {
			return model.BusinessLineConflictCodef("Business line code already exists", nil, nil)
		}
	}

	// Validate parent business line exists if provided and changed
	if businessLine.ParentID != nil && *businessLine.ParentID != "" {
		// Prevent self-referencing
		if *businessLine.ParentID == businessLine.ID {
			return model.BusinessLineInvalidParentf("Business line cannot be its own parent", nil, nil)
		}

		_, err := b.repo.GetByProp(ctx, "id", *businessLine.ParentID)
		if err != nil {
			return model.BusinessLineInvalidParentf("Parent business line does not exist", err, nil)
		}
	}

	updatedBusinessLine := model.BusinessLine{
		ID:       businessLine.ID,
		Name:     businessLine.Name,
		Code:     businessLine.Code,
		ParentID: businessLine.ParentID,
	}

	err = b.repo.Update(ctx, updatedBusinessLine)
	if err != nil {
		return err
	}

	return nil
}
