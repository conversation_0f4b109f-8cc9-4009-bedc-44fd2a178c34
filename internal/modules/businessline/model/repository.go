package model

import "context"

type BusinessLineRepository interface {
	Create(ctx context.Context, businessLine BusinessLine) error
	Update(ctx context.Context, businessLine BusinessLine) error
	GetByProp(ctx context.Context, prop string, value string) (*BusinessLine, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]BusinessLine, error)
	Delete(ctx context.Context, id string) error
	GetSublines(ctx context.Context, businessLineID string) ([]BusinessLine, error)
	GetParentLines(ctx context.Context) ([]BusinessLine, error)
	GetParentLine(ctx context.Context, businessLineID string) (*BusinessLine, error)
}
