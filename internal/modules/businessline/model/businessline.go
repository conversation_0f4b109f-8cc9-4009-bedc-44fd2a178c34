package model

import "time"

type BusinessLine struct {
	ID        string
	Name      string
	Code      string
	ParentID  *string // Self-referencing foreign key (nullable)
	State     string
	CreatedAt *time.Time
	UpdatedAt *time.Time
	DeletedAt *time.Time
}

type BusinessLineCreate struct {
	Name     string
	Code     string
	ParentID *string
	State    string
}

type BusinessLineUpdate struct {
	ID       string
	Name     string
	Code     string
	ParentID *string
	State    string
}

type BusinessLineWithSublines struct {
	BusinessLine BusinessLine
	Sublines     []BusinessLine
	Parent       *BusinessLine
}
