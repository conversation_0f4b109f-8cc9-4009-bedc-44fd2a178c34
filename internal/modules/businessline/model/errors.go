package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	BusinessLineConflictCode     utils.ErrCode = utils.BusinessLineCode + iota
	BusinessLineConflictNameCode
	BusinessLineConflictCodeCode
	BusinessLineNotFoundCode
	BusinessLineInvalidParentCode
)

func BusinessLineConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(BusinessLineConflictCode, message, err, details)
}

func BusinessLineConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(BusinessLineConflictNameCode, message, err, details)
}

func BusinessLineConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(BusinessLineConflictCodeCode, message, err, details)
}

func BusinessLineNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(BusinessLineNotFoundCode, message, err, details)
}

func BusinessLineInvalidParentf(message string, err error, details any) utils.AppErr {
	return utils.NewError(BusinessLineInvalidParentCode, message, err, details)
}
