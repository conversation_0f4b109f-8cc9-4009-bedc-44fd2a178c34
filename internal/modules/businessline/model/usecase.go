package model

import "context"

type BusinessLineUsecase interface {
	Create(ctx context.Context, businessLine BusinessLineCreate) (string, error)
	Update(ctx context.Context, businessLine BusinessLineUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*BusinessLine, error)
	GetAll(ctx context.Context) ([]BusinessLine, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	GetSublines(ctx context.Context, businessLineID string) ([]BusinessLine, error)
	GetSublinesByCode(ctx context.Context, code string) ([]BusinessLine, error)
	GetParentLines(ctx context.Context) ([]BusinessLine, error)
	GetBusinessLineWithDetails(ctx context.Context, businessLineID string) (*BusinessLineWithSublines, error)
	GetBusinessLineWithDetailsByCode(ctx context.Context, code string) (*BusinessLineWithSublines, error)
}
