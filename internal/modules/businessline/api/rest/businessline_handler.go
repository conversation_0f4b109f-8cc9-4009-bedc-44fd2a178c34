package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type BusinessLineHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	GetSublines(w http.ResponseWriter, r *http.Request)
	GetSublinesByCode(w http.ResponseWriter, r *http.Request)
	GetParentLines(w http.ResponseWriter, r *http.Request)
	GetBusinessLineWithDetails(w http.ResponseWriter, r *http.Request)
	GetBusinessLineWithDetailsByCode(w http.ResponseWriter, r *http.Request)
}

type businessLineHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.BusinessLineUsecase
}

func NewBusinessLineHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.BusinessLineUsecase,
) BusinessLineHandler {
	return &businessLineHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
