package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements BusinessLineHandler.
func (b *businessLineHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := b.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to delete business line")
		return
	}

	rest.SuccessDResponse(w, r, "Business line deleted successfully", http.StatusOK)
}
