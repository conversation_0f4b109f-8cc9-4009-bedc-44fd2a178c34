package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type businessLineResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Code      string     `json:"code"`
	ParentID  *string    `json:"parent_id"`
	State     string     `json:"state"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func businessLineToResult(businessLine *model.BusinessLine) businessLineResult {
	return businessLineResult{
		ID:        businessLine.ID,
		Name:      businessLine.Name,
		Code:      businessLine.Code,
		ParentID:  businessLine.ParentID,
		State:     businessLine.State,
		CreatedAt: businessLine.CreatedAt,
		UpdatedAt: businessLine.UpdatedAt,
		DeletedAt: businessLine.DeletedAt,
	}
}

type businessLineWithDetailsResult struct {
	BusinessLine businessLineResult   `json:"business_line"`
	Sublines     []businessLineResult `json:"sublines"`
	Parent       *businessLineResult  `json:"parent"`
}

func businessLineWithDetailsToResult(details *model.BusinessLineWithSublines) businessLineWithDetailsResult {
	result := businessLineWithDetailsResult{
		BusinessLine: businessLineToResult(&details.BusinessLine),
		Sublines:     make([]businessLineResult, len(details.Sublines)),
	}

	for i, subline := range details.Sublines {
		result.Sublines[i] = businessLineToResult(&subline)
	}

	if details.Parent != nil {
		parentResult := businessLineToResult(details.Parent)
		result.Parent = &parentResult
	}

	return result
}

// GetById implements BusinessLineHandler.
func (b *businessLineHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	businessLine, err := b.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get business line by id")
		return
	}

	rest.SuccessDResponse(w, r, businessLineToResult(businessLine), http.StatusOK)
}

// GetAll implements BusinessLineHandler.
func (b *businessLineHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	businessLines, err := b.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get business lines")
		return
	}

	results := make([]businessLineResult, len(businessLines))
	for i, businessLine := range businessLines {
		results[i] = businessLineToResult(&businessLine)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSublines implements BusinessLineHandler.
func (b *businessLineHandler) GetSublines(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	sublines, err := b.useCase.GetSublines(ctx, id)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get sublines")
		return
	}

	results := make([]businessLineResult, len(sublines))
	for i, businessLine := range sublines {
		results[i] = businessLineToResult(&businessLine)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetSublinesByCode implements BusinessLineHandler.
func (b *businessLineHandler) GetSublinesByCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	sublines, err := b.useCase.GetSublinesByCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get sublines by code")
		return
	}

	results := make([]businessLineResult, len(sublines))
	for i, businessLine := range sublines {
		results[i] = businessLineToResult(&businessLine)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetParentLines implements BusinessLineHandler.
func (b *businessLineHandler) GetParentLines(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	parentLines, err := b.useCase.GetParentLines(ctx)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get parent business lines")
		return
	}

	results := make([]businessLineResult, len(parentLines))
	for i, businessLine := range parentLines {
		results[i] = businessLineToResult(&businessLine)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetBusinessLineWithDetails implements BusinessLineHandler.
func (b *businessLineHandler) GetBusinessLineWithDetails(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	details, err := b.useCase.GetBusinessLineWithDetails(ctx, id)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get business line with details")
		return
	}

	rest.SuccessDResponse(w, r, businessLineWithDetailsToResult(details), http.StatusOK)
}

// GetBusinessLineWithDetailsByCode implements BusinessLineHandler.
func (b *businessLineHandler) GetBusinessLineWithDetailsByCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	details, err := b.useCase.GetBusinessLineWithDetailsByCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get business line with details by code")
		return
	}

	rest.SuccessDResponse(w, r, businessLineWithDetailsToResult(details), http.StatusOK)
}
