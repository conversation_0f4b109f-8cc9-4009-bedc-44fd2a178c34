package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.BusinessLineConflictCode:       http.StatusConflict,
	model.BusinessLineConflictNameCode:   http.StatusConflict,
	model.BusinessLineConflictCodeCode:   http.StatusConflict,
	model.BusinessLineNotFoundCode:       http.StatusNotFound,
	model.BusinessLineInvalidParentCode:  http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
