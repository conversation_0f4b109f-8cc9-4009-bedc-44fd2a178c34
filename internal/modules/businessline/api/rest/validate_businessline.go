package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements BusinessLineHandler.
func (b *businessLineHandler) ValidateCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	err := b.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Code validation failed")
		return
	}

	rest.SuccessDResponse(w, r, "Code is available", http.StatusOK)
}
