package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *businessLinePostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.BusinessLine, error) {
	var businessLine model.BusinessLine
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, name, code, parent_id, state, created_at, updated_at, deleted_at
			FROM business_lines
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		err := conn.QueryRow(ctx, query, value).Scan(
			&businessLine.ID,
			&businessLine.Name,
			&businessLine.Code,
			&businessLine.ParentID,
			&businessLine.State,
			&businessLine.CreatedAt,
			&businessLine.UpdatedAt,
			&businessLine.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.BusinessLineNotFoundf("Business line not found", err, nil)
			}
			return utils.InternalErrorf("failed to get business line", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &businessLine, nil
}

func (b *businessLinePostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM business_lines
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		err := conn.QueryRow(ctx, query, value).Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count business lines", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (b *businessLinePostgreRepo) GetAll(ctx context.Context) ([]model.BusinessLine, error) {
	var businessLines []model.BusinessLine
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, parent_id, state, created_at, updated_at, deleted_at
			FROM business_lines
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get business lines", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var businessLine model.BusinessLine
			err := rows.Scan(
				&businessLine.ID,
				&businessLine.Name,
				&businessLine.Code,
				&businessLine.ParentID,
				&businessLine.State,
				&businessLine.UpdatedAt,
				&businessLine.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan business line", err, nil)
			}
			businessLines = append(businessLines, businessLine)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return businessLines, nil
}

func (b *businessLinePostgreRepo) GetSublines(ctx context.Context, businessLineID string) ([]model.BusinessLine, error) {
	var businessLines []model.BusinessLine
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, parent_id, state, created_at, updated_at, deleted_at
			FROM business_lines
			WHERE parent_id = $1 AND deleted_at IS NULL
			ORDER BY name ASC
		`

		rows, err := conn.Query(ctx, query, businessLineID)
		if err != nil {
			return utils.InternalErrorf("failed to get sublines", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var businessLine model.BusinessLine
			err := rows.Scan(
				&businessLine.ID,
				&businessLine.Name,
				&businessLine.Code,
				&businessLine.ParentID,
				&businessLine.State,
				&businessLine.CreatedAt,
				&businessLine.UpdatedAt,
				&businessLine.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan subline", err, nil)
			}
			businessLines = append(businessLines, businessLine)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return businessLines, nil
}

func (b *businessLinePostgreRepo) GetParentLines(ctx context.Context) ([]model.BusinessLine, error) {
	var businessLines []model.BusinessLine
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, parent_id, state, created_at, updated_at, deleted_at
			FROM business_lines
			WHERE parent_id IS NULL AND deleted_at IS NULL
			ORDER BY name ASC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get parent business lines", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var businessLine model.BusinessLine
			err := rows.Scan(
				&businessLine.ID,
				&businessLine.Name,
				&businessLine.Code,
				&businessLine.ParentID,
				&businessLine.State,
				&businessLine.CreatedAt,
				&businessLine.UpdatedAt,
				&businessLine.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan parent business line", err, nil)
			}
			businessLines = append(businessLines, businessLine)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return businessLines, nil
}

func (b *businessLinePostgreRepo) GetParentLine(ctx context.Context, businessLineID string) (*model.BusinessLine, error) {
	// First get the business line to find its parent ID
	businessLine, err := b.GetByProp(ctx, "id", businessLineID)
	if err != nil {
		return nil, err
	}

	if businessLine.ParentID == nil {
		return nil, nil // No parent business line
	}

	// Get the parent business line
	parentBusinessLine, err := b.GetByProp(ctx, "id", *businessLine.ParentID)
	if err != nil {
		return nil, err
	}

	return parentBusinessLine, nil
}
