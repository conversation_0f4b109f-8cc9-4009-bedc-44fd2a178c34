package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type BusinessLinePostgreRepo interface {
	Create(ctx context.Context, businessLine model.BusinessLine) error
	Update(ctx context.Context, businessLine model.BusinessLine) error
	GetByProp(ctx context.Context, prop string, value string) (*model.BusinessLine, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.BusinessLine, error)
	Delete(ctx context.Context, id string) error
	GetSublines(ctx context.Context, businessLineID string) ([]model.BusinessLine, error)
	GetParentLines(ctx context.Context) ([]model.BusinessLine, error)
	GetParentLine(ctx context.Context, businessLineID string) (*model.BusinessLine, error)
}

type businessLinePostgreRepo struct {
	pool *pgxpool.Pool
}

func NewBusinessLinePostgreRepo(pool *pgxpool.Pool) BusinessLinePostgreRepo {
	return &businessLinePostgreRepo{
		pool: pool,
	}
}
