package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *businessLinePostgreRepo) Create(ctx context.Context, businessLine model.BusinessLine) error {
	return pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO business_lines (id, name, code, parent_id, state)
			VALUES ($1, $2, $3, $4, $5)
		`

		_, err := conn.Exec(ctx, query,
			businessLine.ID,
			businessLine.Name,
			businessLine.Code,
			businessLine.ParentID,
			businessLine.State,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create business line", err, nil)
		}

		return nil
	})
}
