package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/repo/pg"
)

type businessLineRepository struct {
	pgRepo pg.BusinessLinePostgreRepo
}

// CountByProp implements model.BusinessLineRepository.
func (b *businessLineRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return b.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.BusinessLineRepository.
func (b *businessLineRepository) Create(ctx context.Context, businessLine model.BusinessLine) error {
	return b.pgRepo.Create(ctx, businessLine)
}

// Delete implements model.BusinessLineRepository.
func (b *businessLineRepository) Delete(ctx context.Context, id string) error {
	return b.pgRepo.Delete(ctx, id)
}

// GetAll implements model.BusinessLineRepository.
func (b *businessLineRepository) GetAll(ctx context.Context) ([]model.BusinessLine, error) {
	return b.pgRepo.GetAll(ctx)
}

// GetByProp implements model.BusinessLineRepository.
func (b *businessLineRepository) GetByProp(ctx context.Context, prop string, value string) (*model.BusinessLine, error) {
	return b.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.BusinessLineRepository.
func (b *businessLineRepository) Update(ctx context.Context, businessLine model.BusinessLine) error {
	return b.pgRepo.Update(ctx, businessLine)
}

// GetSublines implements model.BusinessLineRepository.
func (b *businessLineRepository) GetSublines(ctx context.Context, businessLineID string) ([]model.BusinessLine, error) {
	return b.pgRepo.GetSublines(ctx, businessLineID)
}

// GetParentLines implements model.BusinessLineRepository.
func (b *businessLineRepository) GetParentLines(ctx context.Context) ([]model.BusinessLine, error) {
	return b.pgRepo.GetParentLines(ctx)
}

// GetParentLine implements model.BusinessLineRepository.
func (b *businessLineRepository) GetParentLine(ctx context.Context, businessLineID string) (*model.BusinessLine, error) {
	return b.pgRepo.GetParentLine(ctx, businessLineID)
}

func NewBusinessLineRepository(pgRepo pg.BusinessLinePostgreRepo) model.BusinessLineRepository {
	return &businessLineRepository{
		pgRepo: pgRepo,
	}
}
