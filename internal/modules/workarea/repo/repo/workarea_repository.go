package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/repo/pg"
)

type workAreaRepository struct {
	pgRepo pg.WorkAreaPostgreRepo
}

func (w *workAreaRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return w.pgRepo.CountByProp(ctx, prop, value)
}

func (w *workAreaRepository) Create(ctx context.Context, workArea model.WorkArea) error {
	return w.pgRepo.Create(ctx, workArea)
}

func (w *workAreaRepository) Delete(ctx context.Context, id string) error {
	return w.pgRepo.Delete(ctx, id)
}

func (w *workAreaRepository) GetAll(ctx context.Context) ([]model.WorkArea, error) {
	return w.pgRepo.GetAll(ctx)
}

func (w *workAreaRepository) GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error) {
	return w.pgRepo.GetByProp(ctx, prop, value)
}

func (w *workAreaRepository) Update(ctx context.Context, workArea model.WorkArea) error {
	return w.pgRepo.Update(ctx, workArea)
}

func NewWorkAreaRepository(pgRepo pg.WorkAreaPostgreRepo) model.WorkAreaRepository {
	return &workAreaRepository{
		pgRepo: pgRepo,
	}
}
