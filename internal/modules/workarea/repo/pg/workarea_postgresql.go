package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WorkAreaPostgreRepo interface {
	Create(ctx context.Context, workArea model.WorkArea) error
	Update(ctx context.Context, workArea model.WorkArea) error
	GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.WorkArea, error)
	Delete(ctx context.Context, id string) error
}

type workAreaPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewWorkAreaPostgreRepo(pool *pgxpool.Pool) WorkAreaPostgreRepo {
	return &workAreaPostgreRepo{
		pool: pool,
	}
}
