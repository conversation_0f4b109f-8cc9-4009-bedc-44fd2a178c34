CREATE TABLE dev.work_areas (
    id VARCHAR(255) PRIMARY KEY,
    code VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_work_areas_code ON dev.work_areas(code);
CREATE INDEX idx_work_areas_name ON dev.work_areas(name);
CREATE INDEX idx_work_areas_deleted_at ON dev.work_areas(deleted_at);
CREATE INDEX idx_work_areas_created_at ON dev.work_areas(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_work_areas_updated_at BEFORE UPDATE ON dev.work_areas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
