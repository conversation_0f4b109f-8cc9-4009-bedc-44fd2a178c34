package model

import "context"

type WorkAreaUsecase interface {
	Create(ctx context.Context, workArea WorkAreaCreate) (string, error)
	Update(ctx context.Context, workArea WorkAreaUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*WorkArea, error)
	GetAll(ctx context.Context) ([]WorkArea, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
}
