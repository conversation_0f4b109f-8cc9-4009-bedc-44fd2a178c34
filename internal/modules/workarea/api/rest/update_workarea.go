package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements WorkAreaHandler.
func (w *workAreaHandler) Update(wr http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[workAreaUpdate](wr, r, w.validator)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		return
	}

	if err := w.useCase.Update(ctx, workAreaUpdateToModel(*req)); err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Failed to update work area")
		return
	}

	rest.SuccessResponse(wr, r, http.StatusNoContent)
}
