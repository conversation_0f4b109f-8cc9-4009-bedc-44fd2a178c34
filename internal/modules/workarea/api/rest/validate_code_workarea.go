package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements WorkAreaHandler.
func (w *workAreaHandler) ValidateCode(wr http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	if code == "" {
		rest.ErrorResponse(wr, r, utils.BadRequestf("Code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := w.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, w.log, err)
		respErrHandler(wr, r, err, "Code validation failed")
		return
	}

	rest.SuccessResponse(wr, r, http.StatusOK)
}
