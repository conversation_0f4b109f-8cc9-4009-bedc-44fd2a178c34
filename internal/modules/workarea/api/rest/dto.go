package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
)

type workAreaResult struct {
	ID        string     `json:"id"`
	Code      string     `json:"code"`
	Name      string     `json:"name"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func workAreaToResult(workArea *model.WorkArea) workAreaResult {
	return workAreaResult{
		ID:        workArea.ID,
		Code:      workArea.Code,
		Name:      workArea.Name,
		CreatedAt: workArea.CreatedAt,
		UpdatedAt: workArea.UpdatedAt,
		DeletedAt: workArea.DeletedAt,
	}
}

type workAreaCreate struct {
	Code string `json:"code" validate:"required"`
	Name string `json:"name" validate:"required"`
}

func workAreaCreateToModel(dto workAreaCreate) model.WorkAreaCreate {
	return model.WorkAreaCreate{
		Code: dto.Code,
		Name: dto.Name,
	}
}

type workAreaUpdate struct {
	ID   string `json:"id" validate:"required"`
	Code string `json:"code" validate:"required"`
	Name string `json:"name" validate:"required"`
}

func workAreaUpdateToModel(dto workAreaUpdate) model.WorkAreaUpdate {
	return model.WorkAreaUpdate{
		ID:   dto.ID,
		Code: dto.Code,
		Name: dto.Name,
	}
}
