package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
)

type operationResult struct {
	ID        string     `json:"id"`
	Code      string     `json:"code"`
	Name      string     `json:"name"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func operationToResult(operation *model.Operation) operationResult {
	return operationResult{
		ID:        operation.ID,
		Code:      operation.Code,
		Name:      operation.Name,
		CreatedAt: operation.CreatedAt,
		UpdatedAt: operation.UpdatedAt,
		DeletedAt: operation.DeletedAt,
	}
}

type operationCreate struct {
	Code string `json:"code" validate:"required"`
	Name string `json:"name" validate:"required"`
}

func operationCreateToModel(dto operationCreate) model.OperationCreate {
	return model.OperationCreate{
		Code: dto.Code,
		Name: dto.Name,
	}
}
