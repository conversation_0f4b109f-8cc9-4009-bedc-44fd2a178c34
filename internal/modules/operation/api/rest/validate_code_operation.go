package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateCode implements OperationHandler.
func (o *operationHandler) ValidateCode(w http.ResponseWriter, r *http.Request) {
	code := r.PathValue("code")
	ctx := r.Context()

	if code == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := o.useCase.ValidateCode(ctx, code)
	if err != nil {
		utils.LogErr(ctx, o.log, err)
		respErrHandler(w, r, err, "Code validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
