package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements OperationHandler.
func (o *operationHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	operations, err := o.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, o.log, err)
		respErrHandler(w, r, err, "Failed to get all operations")
		return
	}

	results := make([]operationResult, len(operations))
	for i, operation := range operations {
		results[i] = operationToResult(&operation)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
