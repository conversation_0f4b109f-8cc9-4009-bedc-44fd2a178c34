package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.OperationConflictCode:     http.StatusConflict,
	model.OperationConflictNameCode: http.StatusConflict,
	model.OperationConflictCodeCode: http.StatusConflict,
	model.OperationNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
