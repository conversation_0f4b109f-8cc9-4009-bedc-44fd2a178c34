package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements OperationHandler.
func (o *operationHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[operationCreate](w, r, o.validator)
	if err != nil {
		utils.LogErr(ctx, o.log, err)
		return
	}

	id, err := o.useCase.Create(ctx, operationCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, o.log, err)
		respErrHandler(w, r, err, "Failed to create operation")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
