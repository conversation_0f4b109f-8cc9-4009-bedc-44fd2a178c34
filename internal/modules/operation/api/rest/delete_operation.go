package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements OperationHandler.
func (o *operationHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := o.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, o.log, err)
		respErrHandler(w, r, err, "Failed to delete operation")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
