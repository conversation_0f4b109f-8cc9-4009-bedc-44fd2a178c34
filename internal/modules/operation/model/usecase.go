package model

import "context"

type OperationUsecase interface {
	Create(ctx context.Context, operation OperationCreate) (string, error)
	Update(ctx context.Context, operation OperationUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Operation, error)
	GetAll(ctx context.Context) ([]Operation, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
}
