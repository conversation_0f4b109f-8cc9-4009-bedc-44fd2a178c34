CREATE TABLE dev.operations (
    id VARCHAR(255) PRIMARY KEY,
    code VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_operations_code ON dev.operations(code);
CREATE INDEX idx_operations_name ON dev.operations(name);
CREATE INDEX idx_operations_deleted_at ON dev.operations(deleted_at);
CREATE INDEX idx_operations_created_at ON dev.operations(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_operations_updated_at BEFORE UPDATE ON dev.operations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
