package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (o *operationPostgreRepo) Create(ctx context.Context, operation model.Operation) error {
	return pg.ExecuteInSchema(ctx, o.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO operations (id, code, name)
			VALUES ($1, $2, $3)
		`

		_, err := conn.Exec(ctx, query,
			operation.ID,
			operation.Code,
			operation.Name,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create operation", err, nil)
		}

		return nil
	})
}
