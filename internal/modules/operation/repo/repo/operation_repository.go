package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/repo/pg"
)

type operationRepository struct {
	pgRepo pg.OperationPostgreRepo
}

// CountByProp implements model.OperationRepository.
func (o *operationRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return o.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.OperationRepository.
func (o *operationRepository) Create(ctx context.Context, operation model.Operation) error {
	return o.pgRepo.Create(ctx, operation)
}

// Delete implements model.OperationRepository.
func (o *operationRepository) Delete(ctx context.Context, id string) error {
	return o.pgRepo.Delete(ctx, id)
}

// GetAll implements model.OperationRepository.
func (o *operationRepository) GetAll(ctx context.Context) ([]model.Operation, error) {
	return o.pgRepo.GetAll(ctx)
}

// GetByProp implements model.OperationRepository.
func (o *operationRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Operation, error) {
	return o.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.OperationRepository.
func (o *operationRepository) Update(ctx context.Context, operation model.Operation) error {
	return o.pgRepo.Update(ctx, operation)
}

func NewOperationRepository(pgRepo pg.OperationPostgreRepo) model.OperationRepository {
	return &operationRepository{
		pgRepo: pgRepo,
	}
}
