package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.OperationUsecase.
func (o *operationUsecase) Update(ctx context.Context, operation model.OperationUpdate) error {
	// Get the current operation to check if code/name has changed
	currentOperation, err := o.repo.GetByProp(ctx, "id", operation.ID)
	if err != nil {
		return err
	}

	// Check if code has changed and if it's already in use
	if currentOperation.Code != operation.Code {
		codeExists, err := o.repo.CountByProp(ctx, "code", operation.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if operation code exists", err, nil)
		}

		if codeExists > 0 {
			return model.OperationConflictCodef("Operation code already exists", nil, nil)
		}
	}

	// Check if name has changed and if it's already in use
	if currentOperation.Name != operation.Name {
		nameExists, err := o.repo.CountByProp(ctx, "name", operation.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if operation name exists", err, nil)
		}

		if nameExists > 0 {
			return model.OperationConflictNamef("Operation name already exists", nil, nil)
		}
	}

	updatedOperation := model.Operation{
		ID:   operation.ID,
		Code: operation.Code,
		Name: operation.Name,
	}

	err = o.repo.Update(ctx, updatedOperation)
	if err != nil {
		return err
	}

	return nil
}
