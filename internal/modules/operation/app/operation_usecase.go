package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
)

type operationUsecase struct {
	repo model.OperationRepository
}

// Delete implements model.OperationUsecase.
func (o *operationUsecase) Delete(ctx context.Context, id string) error {
	return o.repo.Delete(ctx, id)
}

// GetAll implements model.OperationUsecase.
func (o *operationUsecase) GetAll(ctx context.Context) ([]model.Operation, error) {
	return o.repo.GetAll(ctx)
}

// GetByProp implements model.OperationUsecase.
func (o *operationUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Operation, error) {
	return o.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.OperationUsecase.
func (o *operationUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := o.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.OperationConflictCodef("Operation code already exists", nil, nil)
	}

	return nil
}

func NewOperationUsecase(repo model.OperationRepository) model.OperationUsecase {
	return &operationUsecase{
		repo: repo,
	}
}
