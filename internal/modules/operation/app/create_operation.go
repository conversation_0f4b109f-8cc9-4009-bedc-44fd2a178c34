package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.OperationUsecase.
func (o *operationUsecase) Create(ctx context.Context, operation model.OperationCreate) (string, error) {
	// Check if code already exists
	codeExists, err := o.repo.CountByProp(ctx, "code", operation.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if operation code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.OperationConflictCodef("Operation code already exists", nil, nil)
	}

	// Check if name already exists
	nameExists, err := o.repo.CountByProp(ctx, "name", operation.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if operation name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.OperationConflictNamef("Operation name already exists", nil, nil)
	}

	newOperation := model.Operation{
		ID:   utils.UniqueId(),
		Code: operation.Code,
		Name: operation.Name,
	}

	err = o.repo.Create(ctx, newOperation)
	if err != nil {
		return "", err
	}

	return newOperation.ID, nil
}
