package model

import "context"

type ProductionFlowRepository interface {
	Create(ctx context.Context, productionFlow ProductionFlow) error
	CreateWithActivities(ctx context.Context, productionFlow ProductionFlow, activities []Activity) error
	Update(ctx context.Context, productionFlow ProductionFlow) error
	GetByProp(ctx context.Context, prop string, value string) (*ProductionFlow, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]ProductionFlow, error)
	Delete(ctx context.Context, id string) error
	GetWithActivities(ctx context.Context, productionFlowID string) (*ProductionFlowWithActivities, error)
	CreateActivity(ctx context.Context, activity Activity) error
	UpdateActivity(ctx context.Context, activity Activity) error
	DeleteActivity(ctx context.Context, activityID string) error
}
