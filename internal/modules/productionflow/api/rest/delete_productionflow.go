package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements ProductionFlowHandler.
func (p *productionFlowHandler) Delete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	id := r.PathValue("id")
	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Production flow ID is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := p.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to delete production flow")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
