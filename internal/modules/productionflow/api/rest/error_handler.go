package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ProductionFlowConflictCode:     http.StatusConflict,
	model.ProductionFlowConflictNameCode: http.StatusConflict,
	model.ProductionFlowConflictCodeCode: http.StatusConflict,
	model.ProductionFlowNotFoundCode:     http.StatusNotFound,
	model.ActivityNotFoundCode:           http.StatusNotFound,
	model.ActivityConflictCode:           http.StatusConflict,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
