package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ProductionFlowHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	CreateWithActivities(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	GetWithActivities(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
}

type productionFlowHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.ProductionFlowUsecase
}

func NewProductionFlowHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.ProductionFlowUsecase,
) ProductionFlowHandler {
	return &productionFlowHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
