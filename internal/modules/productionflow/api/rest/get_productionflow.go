package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements ProductionFlowHandler.
func (p *productionFlowHandler) GetById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	id := r.PathValue("id")
	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Production flow ID is required", nil, nil), http.StatusBadRequest)
		return
	}

	productionFlow, err := p.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production flow")
		return
	}

	response := modelToProductionFlowResponse(*productionFlow)
	rest.SuccessDResponse(w, r, response, http.StatusOK)
}

// GetAll implements ProductionFlowHandler.
func (p *productionFlowHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	productionFlows, err := p.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production flows")
		return
	}

	response := modelToProductionFlowsResponse(productionFlows)
	rest.SuccessDResponse(w, r, response, http.StatusOK)
}

// GetWithActivities implements ProductionFlowHandler.
func (p *productionFlowHandler) GetWithActivities(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	id := r.PathValue("id")
	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Production flow ID is required", nil, nil), http.StatusBadRequest)
		return
	}

	data, err := p.useCase.GetWithActivities(ctx, id)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to get production flow with activities")
		return
	}

	response := modelToProductionFlowWithActivitiesResponse(*data)
	rest.SuccessDResponse(w, r, response, http.StatusOK)
}
