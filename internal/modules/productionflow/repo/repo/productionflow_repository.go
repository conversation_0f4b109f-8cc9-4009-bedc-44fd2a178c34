package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/repo/pg"
)

type productionFlowRepository struct {
	pgRepo pg.ProductionFlowPostgreRepo
}

// Create implements model.ProductionFlowRepository.
func (p *productionFlowRepository) Create(ctx context.Context, productionFlow model.ProductionFlow) error {
	return p.pgRepo.Create(ctx, productionFlow)
}

// CreateWithActivities implements model.ProductionFlowRepository.
func (p *productionFlowRepository) CreateWithActivities(ctx context.Context, productionFlow model.ProductionFlow, activities []model.Activity) error {
	return p.pgRepo.CreateWithActivities(ctx, productionFlow, activities)
}

// Update implements model.ProductionFlowRepository.
func (p *productionFlowRepository) Update(ctx context.Context, productionFlow model.ProductionFlow) error {
	return p.pgRepo.Update(ctx, productionFlow)
}

// GetByProp implements model.ProductionFlowRepository.
func (p *productionFlowRepository) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionFlow, error) {
	return p.pgRepo.GetByProp(ctx, prop, value)
}

// CountByProp implements model.ProductionFlowRepository.
func (p *productionFlowRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return p.pgRepo.CountByProp(ctx, prop, value)
}

// GetAll implements model.ProductionFlowRepository.
func (p *productionFlowRepository) GetAll(ctx context.Context) ([]model.ProductionFlow, error) {
	return p.pgRepo.GetAll(ctx)
}

// Delete implements model.ProductionFlowRepository.
func (p *productionFlowRepository) Delete(ctx context.Context, id string) error {
	return p.pgRepo.Delete(ctx, id)
}

// GetWithActivities implements model.ProductionFlowRepository.
func (p *productionFlowRepository) GetWithActivities(ctx context.Context, productionFlowID string) (*model.ProductionFlowWithActivities, error) {
	return p.pgRepo.GetWithActivities(ctx, productionFlowID)
}

// CreateActivity implements model.ProductionFlowRepository.
func (p *productionFlowRepository) CreateActivity(ctx context.Context, activity model.Activity) error {
	return p.pgRepo.CreateActivity(ctx, activity)
}

// UpdateActivity implements model.ProductionFlowRepository.
func (p *productionFlowRepository) UpdateActivity(ctx context.Context, activity model.Activity) error {
	return p.pgRepo.UpdateActivity(ctx, activity)
}

// DeleteActivity implements model.ProductionFlowRepository.
func (p *productionFlowRepository) DeleteActivity(ctx context.Context, activityID string) error {
	return p.pgRepo.DeleteActivity(ctx, activityID)
}

func NewProductionFlowRepository(pgRepo pg.ProductionFlowPostgreRepo) model.ProductionFlowRepository {
	return &productionFlowRepository{
		pgRepo: pgRepo,
	}
}
