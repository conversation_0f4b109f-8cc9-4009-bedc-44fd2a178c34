package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

// GetWithActivities implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) GetWithActivities(ctx context.Context, productionFlowID string) (*model.ProductionFlowWithActivities, error) {
	// First get the production flow
	productionFlow, err := p.GetByProp(ctx, "id", productionFlowID)
	if err != nil {
		return nil, err
	}

	// Then get activities with work area and operation details
	var activities []model.ActivityDetail
	err = pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT
				a.id, a.production_flow_id, a.work_area_id, a.operation_id, a.index_number,
				a.created_at, a.updated_at, a.deleted_at,
				wa.id, wa.code, wa.name,
				op.id, op.code, op.name
			FROM activities a
			INNER JOIN work_areas wa ON a.work_area_id = wa.id
			INNER JOIN operations op ON a.operation_id = op.id
			WHERE a.production_flow_id = $1
				AND a.deleted_at IS NULL
				AND wa.deleted_at IS NULL
				AND op.deleted_at IS NULL
			ORDER BY a.index_number ASC
		`

		rows, err := conn.Query(ctx, query, productionFlowID)
		if err != nil {
			return utils.InternalErrorf("Failed to get activities", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var activity model.Activity
			var workArea model.WorkAreaInfo
			var operation model.OperationInfo

			err := rows.Scan(
				&activity.ID,
				&activity.ProductionFlowID,
				&activity.WorkAreaID,
				&activity.OperationID,
				&activity.IndexNumber,
				&activity.CreatedAt,
				&activity.UpdatedAt,
				&activity.DeletedAt,
				&workArea.ID,
				&workArea.Code,
				&workArea.Name,
				&operation.ID,
				&operation.Code,
				&operation.Name,
			)
			if err != nil {
				return utils.InternalErrorf("Failed to scan activity", err, nil)
			}

			activityDetail := model.ActivityDetail{
				Activity:  activity,
				WorkArea:  workArea,
				Operation: operation,
			}
			activities = append(activities, activityDetail)
		}

		if err = rows.Err(); err != nil {
			return utils.InternalErrorf("Failed to iterate activities", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	result := &model.ProductionFlowWithActivities{
		ProductionFlow: *productionFlow,
		Activities:     activities,
	}

	return result, nil
}
