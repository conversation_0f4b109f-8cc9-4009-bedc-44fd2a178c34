-- Migration script to add product_id column to production_flows table
-- This script adds a foreign key relationship between production flows and products

-- Step 1: Add the product_id column to production_flows table
ALTER TABLE dev.production_flows 
ADD COLUMN IF NOT EXISTS product_id VARCHAR(255);

-- Step 2: Add foreign key constraint to products table
-- Note: PostgreSQL doesn't support IF NOT EXISTS with ADD CONSTRAINT
-- Use DO block to check if constraint exists before adding
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_production_flows_product_id' 
        AND table_schema = 'dev' 
        AND table_name = 'production_flows'
    ) THEN
        ALTER TABLE dev.production_flows 
        ADD CONSTRAINT fk_production_flows_product_id 
        FOREIGN KEY (product_id) REFERENCES dev.products(id);
    END IF;
END $$;

-- Step 3: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_production_flows_product_id ON dev.production_flows(product_id);

-- Step 4: Add comment to document the column purpose
COMMENT ON COLUMN dev.production_flows.product_id IS 'Foreign key reference to products table - the product this production flow is for';