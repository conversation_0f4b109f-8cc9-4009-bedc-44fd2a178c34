-- Production Flows table
CREATE TABLE dev.production_flows (
    id VARCHAR(255) PRIMARY KEY,
    code VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Activities table (junction table with ordering)
CREATE TABLE dev.activities (
    id VARCHAR(255) PRIMARY KEY,
    production_flow_id VARCHAR(255) NOT NULL,
    work_area_id VARCHAR(255) NOT NULL,
    operation_id VARCHAR(255) NOT NULL,
    index_number INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (production_flow_id) REFERENCES dev.production_flows(id),
    FOREIGN KEY (work_area_id) REFERENCES dev.work_areas(id),
    FOR<PERSON><PERSON><PERSON> KEY (operation_id) REFERENCES dev.operations(id),
    UNIQUE(production_flow_id, index_number)
);

-- Indexes for production_flows
CREATE INDEX idx_production_flows_code ON dev.production_flows(code);
CREATE INDEX idx_production_flows_name ON dev.production_flows(name);
CREATE INDEX idx_production_flows_deleted_at ON dev.production_flows(deleted_at);
CREATE INDEX idx_production_flows_created_at ON dev.production_flows(created_at);

-- Indexes for activities
CREATE INDEX idx_activities_production_flow_id ON dev.activities(production_flow_id);
CREATE INDEX idx_activities_work_area_id ON dev.activities(work_area_id);
CREATE INDEX idx_activities_operation_id ON dev.activities(operation_id);
CREATE INDEX idx_activities_index_number ON dev.activities(index_number);
CREATE INDEX idx_activities_deleted_at ON dev.activities(deleted_at);

-- Triggers for updated_at
CREATE TRIGGER update_production_flows_updated_at BEFORE UPDATE ON dev.production_flows
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON dev.activities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
