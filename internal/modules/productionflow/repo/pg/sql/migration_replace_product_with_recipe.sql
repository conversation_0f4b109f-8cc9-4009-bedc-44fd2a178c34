-- Migration script to replace product_id with recipe_id in production_flows table
-- This script removes the product_id column and adds recipe_id with foreign key to recipes table

-- Step 1: Remove the existing foreign key constraint for product_id
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_production_flows_product_id' 
        AND table_schema = 'dev' 
        AND table_name = 'production_flows'
    ) THEN
        ALTER TABLE dev.production_flows 
        DROP CONSTRAINT fk_production_flows_product_id;
    END IF;
END $$;

-- Step 2: Remove the existing index for product_id
DROP INDEX IF EXISTS dev.idx_production_flows_product_id;

-- Step 3: Drop the product_id column
ALTER TABLE dev.production_flows 
DROP COLUMN IF EXISTS product_id;

-- Step 4: Add the recipe_id column
ALTER TABLE dev.production_flows 
ADD COLUMN IF NOT EXISTS recipe_id VARCHAR(255);

-- Step 5: Add foreign key constraint to recipes table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_production_flows_recipe_id' 
        AND table_schema = 'dev' 
        AND table_name = 'production_flows'
    ) THEN
        ALTER TABLE dev.production_flows 
        ADD CONSTRAINT fk_production_flows_recipe_id 
        FOREIGN KEY (recipe_id) REFERENCES dev.recipes(id);
    END IF;
END $$;

-- Step 6: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_production_flows_recipe_id ON dev.production_flows(recipe_id);

-- Step 7: Add comment to document the column purpose
COMMENT ON COLUMN dev.production_flows.recipe_id IS 'Foreign key reference to recipes table - the recipe this production flow is for';
