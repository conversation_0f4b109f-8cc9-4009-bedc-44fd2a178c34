package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

// Update implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) Update(ctx context.Context, productionFlow model.ProductionFlow) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE production_flows
			SET code = $2, name = $3, recipe_id = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, productionFlow.ID, productionFlow.Code, productionFlow.Name, productionFlow.RecipeID)
		if err != nil {
			return utils.InternalErrorf("Failed to update production flow", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.ProductionFlowNotFoundf("Production flow not found", nil, nil)
		}

		return nil
	})
}

// UpdateActivity implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) UpdateActivity(ctx context.Context, activity model.Activity) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE activities
			SET work_area_id = $2, operation_id = $3, index_number = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			activity.ID,
			activity.WorkAreaID,
			activity.OperationID,
			activity.IndexNumber,
		)
		if err != nil {
			return utils.InternalErrorf("Failed to update activity", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.ActivityNotFoundf("Activity not found", nil, nil)
		}

		return nil
	})
}
