package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ProductionFlowPostgreRepo interface {
	Create(ctx context.Context, productionFlow model.ProductionFlow) error
	CreateWithActivities(ctx context.Context, productionFlow model.ProductionFlow, activities []model.Activity) error
	Update(ctx context.Context, productionFlow model.ProductionFlow) error
	GetByProp(ctx context.Context, prop string, value string) (*model.ProductionFlow, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.ProductionFlow, error)
	Delete(ctx context.Context, id string) error
	GetWithActivities(ctx context.Context, productionFlowID string) (*model.ProductionFlowWithActivities, error)
	CreateActivity(ctx context.Context, activity model.Activity) error
	UpdateActivity(ctx context.Context, activity model.Activity) error
	DeleteActivity(ctx context.Context, activityID string) error
}

type productionFlowPostgreRepo struct {
	db *pgxpool.Pool
}

func NewProductionFlowPostgreRepo(db *pgxpool.Pool) ProductionFlowPostgreRepo {
	return &productionFlowPostgreRepo{
		db: db,
	}
}
