package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// GetByProp implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.ProductionFlow, error) {
	var productionFlow model.ProductionFlow
	err := pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection
		allowedProps := map[string]bool{
			"id":         true,
			"code":       true,
			"name":       true,
			"product_id": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, code, name, recipe_id, created_at, updated_at, deleted_at
			FROM production_flows
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&productionFlow.ID,
			&productionFlow.Code,
			&productionFlow.Name,
			&productionFlow.RecipeID,
			&productionFlow.CreatedAt,
			&productionFlow.UpdatedAt,
			&productionFlow.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.ProductionFlowNotFoundf("Production flow not found", err, nil)
			}
			return utils.InternalErrorf("Failed to get production flow", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &productionFlow, nil
}

// CountByProp implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection
		allowedProps := map[string]bool{
			"id":         true,
			"code":       true,
			"name":       true,
			"product_id": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM production_flows
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("Failed to count production flows", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

// GetAll implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) GetAll(ctx context.Context) ([]model.ProductionFlow, error) {
	var productionFlows []model.ProductionFlow
	err := pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, name, recipe_id, created_at, updated_at, deleted_at
			FROM production_flows
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("Failed to get production flows", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var productionFlow model.ProductionFlow
			err := rows.Scan(
				&productionFlow.ID,
				&productionFlow.Code,
				&productionFlow.Name,
				&productionFlow.RecipeID,
				&productionFlow.CreatedAt,
				&productionFlow.UpdatedAt,
				&productionFlow.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("Failed to scan production flow", err, nil)
			}
			productionFlows = append(productionFlows, productionFlow)
		}

		if err = rows.Err(); err != nil {
			return utils.InternalErrorf("Failed to iterate production flows", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return productionFlows, nil
}
