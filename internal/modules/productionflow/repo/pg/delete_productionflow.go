package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

// Delete implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start a transaction to delete production flow and its activities
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("Failed to start transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// First soft delete all activities
		activitiesQuery := `
			UPDATE activities
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE production_flow_id = $1 AND deleted_at IS NULL
		`

		_, err = tx.Exec(ctx, activitiesQuery, id)
		if err != nil {
			return utils.InternalErrorf("Failed to delete activities", err, nil)
		}

		// Then soft delete the production flow
		productionFlowQuery := `
			UPDATE production_flows
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := tx.Exec(ctx, productionFlowQuery, id)
		if err != nil {
			return utils.InternalErrorf("Failed to delete production flow", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.ProductionFlowNotFoundf("Production flow not found", nil, nil)
		}

		// Commit the transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("Failed to commit transaction", err, nil)
		}

		return nil
	})
}

// DeleteActivity implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) DeleteActivity(ctx context.Context, activityID string) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE activities
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, activityID)
		if err != nil {
			return utils.InternalErrorf("Failed to delete activity", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.ActivityNotFoundf("Activity not found", nil, nil)
		}

		return nil
	})
}
