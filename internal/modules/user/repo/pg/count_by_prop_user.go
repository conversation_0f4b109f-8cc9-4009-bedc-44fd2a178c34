package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *userPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int

	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection
		allowedProps := map[string]bool{
			"id":    true,
			"name":  true,
			"email": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM users
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)

		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count users", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}
