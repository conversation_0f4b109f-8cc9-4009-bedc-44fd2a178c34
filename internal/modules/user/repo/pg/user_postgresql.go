package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type UserPostgreRepo interface {
	Create(ctx context.Context, user model.User) error
	Update(ctx context.Context, user model.User) error
	GetByProp(ctx context.Context, prop string, value string) (*model.User, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.User, error)
	Delete(ctx context.Context, id string) error
}

type userPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewUserPostgreRepo(pool *pgxpool.Pool) UserPostgreRepo {
	return &userPostgreRepo{
		pool: pool,
	}
}
