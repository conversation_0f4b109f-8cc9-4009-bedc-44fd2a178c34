package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *userPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE users
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete user", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.UserNotFoundf(
				fmt.Sprintf("user with id: %s not found", id),
				fmt.Errorf("user not found or already deleted"),
				nil,
			)
		}

		return nil
	})
}
