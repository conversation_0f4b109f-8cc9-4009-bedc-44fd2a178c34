package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

// GetAll implements UserPostgreSQLRepo.
func (u *userPostgreRepo) GetAll(ctx context.Context) ([]model.User, error) {
	var users []model.User

	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, email, hashed_pw, created_at, updated_at, deleted_at
			FROM users
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to query users", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var user model.User
			err := rows.Scan(
				&user.ID,
				&user.Name,
				&user.Email,
				&user.HashedPW,
				&user.CreatedAt,
				&user.UpdatedAt,
				&user.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan user row", err, nil)
			}
			users = append(users, user)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("error iterating over rows", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return users, nil
}
