CREATE TABLE dev.users (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    hashed_pw VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_users_email ON dev.users(email);
CREATE INDEX idx_users_deleted_at ON dev.users(deleted_at);
CREATE INDEX idx_users_created_at ON dev.users(created_at);