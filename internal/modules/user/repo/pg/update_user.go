package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *userPostgreRepo) Update(ctx context.Context, user model.User) error {
	return pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE users
			SET name = $2, email = $3, hashed_pw = $4
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			user.ID,
			user.Name,
			user.Email,
			user.HashedPW,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update user: %w", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.UserNotFoundf("user not found or already deleted", nil, nil)
		}

		return nil
	})
}
