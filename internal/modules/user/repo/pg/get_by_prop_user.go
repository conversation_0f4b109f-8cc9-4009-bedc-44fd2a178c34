package pg

import (
	"context"
	"errors"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// GetByProp implements UserPostgreSQLRepo.
func (u *userPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.User, error) {
	var user model.User

	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":    true,
			"name":  true,
			"email": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, name, email, hashed_pw, created_at, updated_at, deleted_at
			FROM users
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)

		err := row.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.HashedPW,
			&user.CreatedAt,
			&user.UpdatedAt,
			&user.DeletedAt,
		)

		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return model.UserNotFoundf(fmt.Sprintf("user with %s: %s not found", prop, value), err, nil)
			}
			return utils.InternalErrorf("failed to get user by id", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &user, nil
}
