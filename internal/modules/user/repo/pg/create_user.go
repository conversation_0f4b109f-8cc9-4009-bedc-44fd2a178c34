package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *userPostgreRepo) Create(ctx context.Context, user model.User) error {
	return pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO users (id, name, email, hashed_pw)
			VALUES ($1, $2, $3, $4)
		`

		_, err := conn.Exec(ctx, query,
			user.ID,
			user.Name,
			user.Email,
			user.HashedPW,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create user", err, nil)
		}

		return nil
	})
}
