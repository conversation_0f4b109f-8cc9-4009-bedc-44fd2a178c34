package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/repo/pg"
)

type userRepository struct {
	pgRepo pg.UserPostgreRepo
}

// CountByProp implements model.UserRepository.
func (u *userRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return u.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.UserRepository.
func (u *userRepository) Create(ctx context.Context, user model.User) error {
	return u.pgRepo.Create(ctx, user)
}

// Delete implements model.UserRepository.
func (u *userRepository) Delete(ctx context.Context, id string) error {
	return u.pgRepo.Delete(ctx, id)
}

// GetAll implements model.UserRepository.
func (u *userRepository) GetAll(ctx context.Context) ([]model.User, error) {
	return u.pgRepo.GetAll(ctx)
}

// GetByProp implements model.UserRepository.
func (u *userRepository) GetByProp(ctx context.Context, prop string, value string) (*model.User, error) {
	return u.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.UserRepository.
func (u *userRepository) Update(ctx context.Context, user model.User) error {
	return u.pgRepo.Update(ctx, user)
}

func NewUserRepository(pgRepo pg.UserPostgreRepo) model.UserRepository {
	return &userRepository{
		pgRepo: pgRepo,
	}
}
