package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	ConflictCode utils.ErrCode = utils.UserCode + iota
	ConflictEmailCode
	ConflictNameCode
	InvalidEmailCode
	UserNotFoundCode
)

func Conflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ConflictCode, message, err, details)
}

func ConflictEmailf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ConflictEmailCode, message, err, details)
}

func ConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(ConflictNameCode, message, err, details)
}

func InvalidEmailf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InvalidEmailCode, message, err, details)
}

func UserNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(UserNotFoundCode, message, err, details)
}
