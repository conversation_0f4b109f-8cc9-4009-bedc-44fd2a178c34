package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.UserUsecase.
func (u *userUsecase) Update(ctx context.Context, user model.UserUpdate) error {
	isEmail := utils.IsEmail(user.Email)
	if !isEmail {
		return model.InvalidEmailf("Invalid email", nil, nil)
	}

	// Get the current user to check if email/name has changed
	currentUser, err := u.repo.GetByProp(ctx, "id", user.ID)
	if err != nil {
		return err
	}

	// Check if email has changed and if it's already in use
	if currentUser.Email != user.Email {
		emailExists, err := u.repo.CountByProp(ctx, "email", user.Email)
		if err != nil {
			return utils.InternalErrorf("Failed to check if email exists", err, nil)
		}

		if emailExists > 0 {
			return model.ConflictEmailf("Email already exists", nil, nil)
		}
	}

	// Check if name has changed and if it's already in use
	if currentUser.Name != user.Name {
		nameExists, err := u.repo.CountByProp(ctx, "name", user.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if name exists", err, nil)
		}

		if nameExists > 0 {
			return model.ConflictNamef("Name already exists", nil, nil)
		}
	}

	// Hash the password if provided
	var hashedPW string
	if user.Password != "" {
		var err error
		hashedPW, err = utils.GenerateHashedPassword(user.Password)
		if err != nil {
			return utils.InternalErrorf("Failed to generate hashed password", err, nil)
		}
	} else {
		// Keep the current password
		hashedPW = currentUser.HashedPW
	}

	updatedUser := model.User{
		ID:       user.ID,
		Name:     user.Name,
		Email:    user.Email,
		HashedPW: hashedPW,
	}

	err = u.repo.Update(ctx, updatedUser)
	if err != nil {
		return err
	}

	return nil
}
