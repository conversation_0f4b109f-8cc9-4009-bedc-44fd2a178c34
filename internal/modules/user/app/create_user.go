package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.UserUsecase.
func (u *userUsecase) Create(ctx context.Context, user model.UserCreate) (string, error) {
	isEmail := utils.IsEmail(user.Email)
	if !isEmail {
		return "", model.InvalidEmailf("Invalid email", nil, nil)
	}

	emailExists, err := u.repo.CountByProp(ctx, "email", user.Email)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if email exists", err, nil)
	}

	if emailExists > 0 {
		return "", model.ConflictEmailf("Email already exists", nil, nil)
	}

	nameExists, err := u.repo.CountByProp(ctx, "name", user.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ConflictNamef("Name already exists", nil, nil)
	}

	hashedPW, err := utils.GenerateHashedPassword(user.Password)
	if err != nil {
		return "", utils.InternalErrorf("Failed to generate hashed password", err, nil)
	}

	newUser := model.User{
		ID:       utils.UniqueId(),
		Name:     user.Name,
		Email:    user.Email,
		HashedPW: hashedPW,
	}

	err = u.repo.Create(ctx, newUser)
	if err != nil {
		return "", err
	}

	return newUser.ID, nil
}
