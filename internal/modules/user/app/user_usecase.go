package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
)

type userUsecase struct {
	repo model.UserRepository
}

// Delete implements model.UserUsecase.
func (u *userUsecase) Delete(ctx context.Context, id string) error {
	return u.repo.Delete(ctx, id)
}

// GetAll implements model.UserUsecase.
func (u *userUsecase) GetAll(ctx context.Context) ([]model.User, error) {
	return u.repo.GetAll(ctx)
}

// GetByProp implements model.UserUsecase.
func (u *userUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.User, error) {
	return u.repo.GetByProp(ctx, prop, value)
}

func NewUserUsecase(repo model.UserRepository) model.UserUsecase {
	return &userUsecase{
		repo: repo,
	}
}
