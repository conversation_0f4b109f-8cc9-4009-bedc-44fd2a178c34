package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements UserHandler.
func (u *userHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	user, err := u.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to get user by id")
		return
	}

	rest.SuccessDResponse(w, r, userTo<PERSON><PERSON><PERSON>(user), http.StatusOK)
}
