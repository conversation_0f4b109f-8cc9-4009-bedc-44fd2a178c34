package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements UserHandler.
func (u *userHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[model.UserUpdate](w, r, u.validator)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		return
	}

	if err := u.useCase.Update(ctx, *req); err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to update user")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
