package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements UserHandler.
func (u *userHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	users, err := u.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to get all users")
		return
	}

	result := make([]userResult, len(users))
	for i, user := range users {
		result[i] = userToResult(&user)
	}

	rest.SuccessDResponse(w, r, result, http.StatusOK)
}
