package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ConflictCode:      http.StatusConflict,
	model.ConflictEmailCode: http.StatusConflict,
	model.ConflictNameCode:  http.StatusConflict,
	model.InvalidEmailCode:  http.StatusBadRequest,
	model.UserNotFoundCode:  http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
