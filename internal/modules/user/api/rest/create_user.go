package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements UserHandler.
func (u *userHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[userCreate](w, r, u.validator)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		return
	}

	id, err := u.useCase.Create(ctx, userCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to create user")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
