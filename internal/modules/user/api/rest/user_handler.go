package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type UserHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type userHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.UserUsecase
}

func NewUserHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.UserUsecase,
) UserHandler {
	return &userHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
