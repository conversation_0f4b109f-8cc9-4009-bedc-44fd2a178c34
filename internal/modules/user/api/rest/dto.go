package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
)

type userResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Email     string     `json:"email"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func userToResult(user *model.User) userResult {
	return userResult{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
		DeletedAt: user.DeletedAt,
	}
}

type userCreate struct {
	Name     string `json:"name"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

func userCreateToModel(user userCreate) model.UserCreate {
	return model.UserCreate{
		Name:     user.Name,
		Email:    user.Email,
		Password: user.Password,
	}
}
