package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
)

type inventoryUsecase struct {
	repo model.InventoryRepository
}

// Delete implements model.InventoryUsecase.
func (i *inventoryUsecase) Delete(ctx context.Context, id string) error {
	return i.repo.Delete(ctx, id)
}

// GetAll implements model.InventoryUsecase.
func (i *inventoryUsecase) GetAll(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.repo.GetAll(ctx)
}

// GetAllWithDetails implements model.InventoryUsecase.
func (i *inventoryUsecase) GetAllWithDetails(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.repo.GetAllWithDetails(ctx)
}

// GetByProp implements model.InventoryUsecase.
func (i *inventoryUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Inventory, error) {
	return i.repo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.InventoryUsecase.
func (i *inventoryUsecase) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.InventoryWithDetails, error) {
	return i.repo.GetByPropWithDetails(ctx, prop, value)
}

// GetByWarehouseID implements model.InventoryUsecase.
func (i *inventoryUsecase) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.InventoryWithDetails, error) {
	return i.repo.GetByWarehouseID(ctx, warehouseID)
}

// GetByProductID implements model.InventoryUsecase.
func (i *inventoryUsecase) GetByProductID(ctx context.Context, productID string) ([]model.InventoryWithDetails, error) {
	return i.repo.GetByProductID(ctx, productID)
}

// GetByBatchCode implements model.InventoryUsecase.
func (i *inventoryUsecase) GetByBatchCode(ctx context.Context, batchCode string) ([]model.InventoryWithDetails, error) {
	return i.repo.GetByBatchCode(ctx, batchCode)
}

// GetLowStockItems implements model.InventoryUsecase.
func (i *inventoryUsecase) GetLowStockItems(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.repo.GetLowStockItems(ctx)
}

// GetExpiringItems implements model.InventoryUsecase.
func (i *inventoryUsecase) GetExpiringItems(ctx context.Context, days int) ([]model.InventoryWithDetails, error) {
	return i.repo.GetExpiringItems(ctx, days)
}

func NewInventoryUsecase(repo model.InventoryRepository) model.InventoryUsecase {
	return &inventoryUsecase{
		repo: repo,
	}
}
