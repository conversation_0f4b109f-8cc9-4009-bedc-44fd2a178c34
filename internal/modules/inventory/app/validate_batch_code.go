package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// ValidateBatchCode implements model.InventoryUsecase.
func (i *inventoryUsecase) ValidateBatchCode(ctx context.Context, warehouseID, productID, batchCode string) error {
	// Check if the combination of warehouse_id, product_id, and batch_code already exists
	count, err := i.repo.CountByWarehouseProductBatch(ctx, warehouseID, productID, batchCode)
	if err != nil {
		return utils.InternalErrorf("Failed to check if batch code exists", err, nil)
	}

	if count > 0 {
		return model.InventoryConflictBatchCodef("Batch code already exists for this warehouse and product combination", nil, nil)
	}

	return nil
}
