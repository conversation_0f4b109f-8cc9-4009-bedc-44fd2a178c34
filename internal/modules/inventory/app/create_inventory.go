package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.InventoryUsecase.
func (i *inventoryUsecase) Create(ctx context.Context, inventory model.InventoryCreate) (string, error) {
	// Validate that the combination of warehouse_id, product_id, and batch_code doesn't already exist
	err := i.ValidateBatchCode(ctx, inventory.WarehouseID, inventory.ProductID, inventory.BatchCode)
	if err != nil {
		return "", err
	}

	// Validate stock values
	if inventory.CurrentStock < 0 {
		return "", model.InventoryNegativeStockf("Current stock cannot be negative", nil, nil)
	}
	if inventory.ReservedStock < 0 {
		return "", model.InventoryNegativeStockf("Reserved stock cannot be negative", nil, nil)
	}
	if inventory.AvailableStock < 0 {
		return "", model.InventoryNegativeStockf("Available stock cannot be negative", nil, nil)
	}
	if inventory.MinStock < 0 {
		return "", model.InventoryNegativeStockf("Min stock cannot be negative", nil, nil)
	}
	if inventory.MaxStock < 0 {
		return "", model.InventoryNegativeStockf("Max stock cannot be negative", nil, nil)
	}
	if inventory.ReorderPoint < 0 {
		return "", model.InventoryNegativeStockf("Reorder point cannot be negative", nil, nil)
	}

	// Validate stock relationships
	if inventory.MaxStock > 0 && inventory.MinStock > inventory.MaxStock {
		return "", model.InventoryValidationErrorf("Min stock cannot be greater than max stock", nil, nil)
	}
	if inventory.ReorderPoint > inventory.MaxStock && inventory.MaxStock > 0 {
		return "", model.InventoryValidationErrorf("Reorder point cannot be greater than max stock", nil, nil)
	}
	if inventory.CurrentStock < inventory.ReservedStock {
		return "", model.InventoryValidationErrorf("Current stock cannot be less than reserved stock", nil, nil)
	}

	// Validate expiration date
	if inventory.ExpirationDate != nil && inventory.ExpirationDate.Before(time.Now()) {
		return "", model.InventoryExpiredBatchf("Expiration date cannot be in the past", nil, nil)
	}

	// Calculate available stock if not provided
	if inventory.AvailableStock == 0 {
		inventory.AvailableStock = inventory.CurrentStock - inventory.ReservedStock
	}

	// Create inventory entity
	id := ulid.Make().String()
	inventoryEntity := model.Inventory{
		ID:               id,
		WarehouseID:      inventory.WarehouseID,
		ProductID:        inventory.ProductID,
		BatchCode:        inventory.BatchCode,
		ExpirationDate:   inventory.ExpirationDate,
		CurrentStock:     inventory.CurrentStock,
		ReservedStock:    inventory.ReservedStock,
		AvailableStock:   inventory.AvailableStock,
		MinStock:         inventory.MinStock,
		MaxStock:         inventory.MaxStock,
		ReorderPoint:     inventory.ReorderPoint,
		LastMovementDate: inventory.LastMovementDate,
	}

	// Save to repository
	err = i.repo.Create(ctx, inventoryEntity)
	if err != nil {
		return "", utils.InternalErrorf("Failed to create inventory", err, nil)
	}

	return id, nil
}
