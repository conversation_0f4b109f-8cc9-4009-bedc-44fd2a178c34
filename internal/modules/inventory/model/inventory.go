package model

import (
	"time"

	brandModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
)

type Inventory struct {
	ID               string
	WarehouseID      string
	ProductID        string
	BatchCode        string
	ExpirationDate   *time.Time
	CurrentStock     float64
	ReservedStock    float64
	AvailableStock   float64
	MinStock         float64
	MaxStock         float64
	ReorderPoint     float64
	LastMovementDate *time.Time
	CreatedAt        *time.Time
	UpdatedAt        *time.Time
	DeletedAt        *time.Time
}

type InventoryCreate struct {
	WarehouseID      string
	ProductID        string
	BatchCode        string
	ExpirationDate   *time.Time
	CurrentStock     float64
	ReservedStock    float64
	AvailableStock   float64
	MinStock         float64
	MaxStock         float64
	ReorderPoint     float64
	LastMovementDate *time.Time
}

type InventoryUpdate struct {
	ID               string
	WarehouseID      string
	ProductID        string
	BatchCode        string
	ExpirationDate   *time.Time
	CurrentStock     float64
	ReservedStock    float64
	AvailableStock   float64
	MinStock         float64
	MaxStock         float64
	ReorderPoint     float64
	LastMovementDate *time.Time
}

// InventoryWithDetails includes complete information about product, brand, categories, and warehouse
type InventoryWithDetails struct {
	ID               string
	WarehouseID      string
	ProductID        string
	BatchCode        string
	ExpirationDate   *time.Time
	CurrentStock     float64
	ReservedStock    float64
	AvailableStock   float64
	MinStock         float64
	MaxStock         float64
	ReorderPoint     float64
	LastMovementDate *time.Time
	CreatedAt        *time.Time
	UpdatedAt        *time.Time
	DeletedAt        *time.Time
	// Related entities
	Product   productModel.Product
	Brand     brandModel.Brand
	Warehouse warehouseModel.Warehouse
}
