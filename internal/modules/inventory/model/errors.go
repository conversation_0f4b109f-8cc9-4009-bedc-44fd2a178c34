package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	InventoryConflictCode utils.ErrCode = utils.InventoryCode + iota
	InventoryConflictBatchCodeCode
	InventoryNotFoundCode
	InventoryValidationErrorCode
	InventoryInsufficientStockCode
	InventoryExpiredBatchCode
	InventoryNegativeStockCode
)

func InventoryConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryConflictCode, message, err, details)
}

func InventoryConflictBatchCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryConflictBatchCodeCode, message, err, details)
}

func InventoryNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryNotFoundCode, message, err, details)
}

func InventoryValidationErrorf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryValidationErrorCode, message, err, details)
}

func InventoryInsufficientStockf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryInsufficientStockCode, message, err, details)
}

func InventoryExpiredBatchf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryExpiredBatchCode, message, err, details)
}

func InventoryNegativeStockf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InventoryNegativeStockCode, message, err, details)
}
