package model

import "context"

type InventoryRepository interface {
	Create(ctx context.Context, inventory Inventory) error
	Update(ctx context.Context, inventory Inventory) error
	GetByProp(ctx context.Context, prop string, value string) (*Inventory, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]InventoryWithDetails, error)
	GetAllWithDetails(ctx context.Context) ([]InventoryWithDetails, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*InventoryWithDetails, error)
	GetByWarehouseID(ctx context.Context, warehouseID string) ([]InventoryWithDetails, error)
	GetByProductID(ctx context.Context, productID string) ([]InventoryWithDetails, error)
	GetByBatchCode(ctx context.Context, batchCode string) ([]InventoryWithDetails, error)
	GetLowStockItems(ctx context.Context) ([]InventoryWithDetails, error)
	GetExpiringItems(ctx context.Context, days int) ([]InventoryWithDetails, error)
	Delete(ctx context.Context, id string) error
	CountByWarehouseProductBatch(ctx context.Context, warehouseID, productID, batchCode string) (int, error)
}
