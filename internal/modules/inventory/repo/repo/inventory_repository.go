package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/repo/pg"
)

type inventoryRepository struct {
	pgRepo pg.InventoryPostgreRepo
}

// CountByProp implements model.InventoryRepository.
func (i *inventoryRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return i.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.InventoryRepository.
func (i *inventoryRepository) Create(ctx context.Context, inventory model.Inventory) error {
	return i.pgRepo.Create(ctx, inventory)
}

// Delete implements model.InventoryRepository.
func (i *inventoryRepository) Delete(ctx context.Context, id string) error {
	return i.pgRepo.Delete(ctx, id)
}

// GetAll implements model.InventoryRepository.
func (i *inventoryRepository) GetAll(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetAll(ctx)
}

// GetAllWithDetails implements model.InventoryRepository.
func (i *inventoryRepository) GetAllWithDetails(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetAllWithDetails(ctx)
}

// GetByProp implements model.InventoryRepository.
func (i *inventoryRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Inventory, error) {
	return i.pgRepo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.InventoryRepository.
func (i *inventoryRepository) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.InventoryWithDetails, error) {
	return i.pgRepo.GetByPropWithDetails(ctx, prop, value)
}

// GetByWarehouseID implements model.InventoryRepository.
func (i *inventoryRepository) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetByWarehouseID(ctx, warehouseID)
}

// GetByProductID implements model.InventoryRepository.
func (i *inventoryRepository) GetByProductID(ctx context.Context, productID string) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetByProductID(ctx, productID)
}

// GetByBatchCode implements model.InventoryRepository.
func (i *inventoryRepository) GetByBatchCode(ctx context.Context, batchCode string) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetByBatchCode(ctx, batchCode)
}

// GetLowStockItems implements model.InventoryRepository.
func (i *inventoryRepository) GetLowStockItems(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetLowStockItems(ctx)
}

// GetExpiringItems implements model.InventoryRepository.
func (i *inventoryRepository) GetExpiringItems(ctx context.Context, days int) ([]model.InventoryWithDetails, error) {
	return i.pgRepo.GetExpiringItems(ctx, days)
}

// Update implements model.InventoryRepository.
func (i *inventoryRepository) Update(ctx context.Context, inventory model.Inventory) error {
	return i.pgRepo.Update(ctx, inventory)
}

// CountByWarehouseProductBatch implements model.InventoryRepository.
func (i *inventoryRepository) CountByWarehouseProductBatch(ctx context.Context, warehouseID, productID, batchCode string) (int, error) {
	return i.pgRepo.CountByWarehouseProductBatch(ctx, warehouseID, productID, batchCode)
}

func NewInventoryRepository(pgRepo pg.InventoryPostgreRepo) model.InventoryRepository {
	return &inventoryRepository{
		pgRepo: pgRepo,
	}
}
