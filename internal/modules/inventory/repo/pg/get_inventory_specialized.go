package pg

import (
	"context"
	"fmt"

	brandModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (i *inventoryPostgreRepo) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.InventoryWithDetails, error) {
	var inventory model.InventoryWithDetails
	var product productModel.Product
	var brand brandModel.Brand
	var warehouse warehouseModel.Warehouse

	err := pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT 
				i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date,
				i.created_at, i.updated_at, i.deleted_at,
				-- Product information
				p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				-- Brand information
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				-- Warehouse information
				w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at,
				-- Category IDs array
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
			FROM inventory i
			JOIN products p ON i.product_id = p.id
			JOIN brands b ON p.brand_id = b.id
			JOIN warehouses w ON i.warehouse_id = w.id
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE i.%s = $1 
			  AND i.deleted_at IS NULL 
			  AND p.deleted_at IS NULL 
			  AND b.deleted_at IS NULL 
			  AND w.deleted_at IS NULL
			GROUP BY 
				i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date, i.created_at,
				i.updated_at, i.deleted_at,
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				w.id, w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at
		`, prop)

		row := conn.QueryRow(ctx, query, value)

		err := row.Scan(
			// Inventory fields
			&inventory.ID, &inventory.WarehouseID, &inventory.ProductID,
			&inventory.BatchCode, &inventory.ExpirationDate, &inventory.CurrentStock,
			&inventory.ReservedStock, &inventory.AvailableStock, &inventory.MinStock,
			&inventory.MaxStock, &inventory.ReorderPoint, &inventory.LastMovementDate,
			&inventory.CreatedAt, &inventory.UpdatedAt, &inventory.DeletedAt,
			// Product fields
			&product.Name, &product.ImageURL, &product.CommercialName,
			&product.Code, &product.SKUCode, &product.MeasurementUnitID,
			&product.State, &product.Description, &product.CanBeSold,
			&product.CanBePurchased, &product.CostPrice, &product.CostPriceTotal,
			&product.CreatedAt, &product.UpdatedAt, &product.DeletedAt,
			// Brand fields
			&brand.ID, &brand.Name, &brand.Code,
			&brand.CreatedAt, &brand.UpdatedAt, &brand.DeletedAt,
			// Warehouse fields
			&warehouse.Name, &warehouse.Code, &warehouse.Type,
			&warehouse.Category, &warehouse.Description, &warehouse.Address,
			&warehouse.IsActive, &warehouse.IsSystemWarehouse,
			&warehouse.CreatedAt, &warehouse.UpdatedAt, &warehouse.DeletedAt,
			// Category IDs
			&product.CategoryIDs,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.InventoryNotFoundf("Inventory not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get inventory by prop with details", err, nil)
		}

		// Set IDs for related entities
		product.ID = inventory.ProductID
		product.BrandID = brand.ID
		warehouse.ID = inventory.WarehouseID

		inventory.Product = product
		inventory.Brand = brand
		inventory.Warehouse = warehouse

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &inventory, nil
}

func (i *inventoryPostgreRepo) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.InventoryWithDetails, error) {
	return i.getInventoriesByFilter(ctx, "i.warehouse_id = $1", warehouseID)
}

func (i *inventoryPostgreRepo) GetByProductID(ctx context.Context, productID string) ([]model.InventoryWithDetails, error) {
	return i.getInventoriesByFilter(ctx, "i.product_id = $1", productID)
}

func (i *inventoryPostgreRepo) GetByBatchCode(ctx context.Context, batchCode string) ([]model.InventoryWithDetails, error) {
	return i.getInventoriesByFilter(ctx, "i.batch_code = $1", batchCode)
}

func (i *inventoryPostgreRepo) GetLowStockItems(ctx context.Context) ([]model.InventoryWithDetails, error) {
	return i.getInventoriesByFilter(ctx, "i.available_stock <= i.reorder_point", nil)
}

func (i *inventoryPostgreRepo) GetExpiringItems(ctx context.Context, days int) ([]model.InventoryWithDetails, error) {
	return i.getInventoriesByFilter(ctx, "i.expiration_date IS NOT NULL AND i.expiration_date <= CURRENT_DATE + INTERVAL '%d days'", days)
}

// Helper method to get inventories with a custom filter
func (i *inventoryPostgreRepo) getInventoriesByFilter(ctx context.Context, whereClause string, param interface{}) ([]model.InventoryWithDetails, error) {
	var inventories []model.InventoryWithDetails

	err := pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		baseQuery := `
			SELECT
				i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date,
				i.created_at, i.updated_at, i.deleted_at,
				-- Product information
				p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				-- Brand information
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				-- Warehouse information
				w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at,
				-- Category IDs array
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
			FROM inventory i
			JOIN products p ON i.product_id = p.id
			JOIN brands b ON p.brand_id = b.id
			JOIN warehouses w ON i.warehouse_id = w.id
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE %s
			  AND i.deleted_at IS NULL
			  AND p.deleted_at IS NULL
			  AND b.deleted_at IS NULL
			  AND w.deleted_at IS NULL
			GROUP BY
				i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date, i.created_at,
				i.updated_at, i.deleted_at,
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				w.id, w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at
			ORDER BY i.created_at DESC
		`

		var query string
		var rows pgx.Rows
		var err error

		if param != nil {
			query = fmt.Sprintf(baseQuery, whereClause)
			rows, err = conn.Query(ctx, query, param)
		} else {
			query = fmt.Sprintf(baseQuery, whereClause)
			rows, err = conn.Query(ctx, query)
		}

		if err != nil {
			return utils.InternalErrorf("failed to get inventories by filter", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var inventory model.InventoryWithDetails
			var product productModel.Product
			var brand brandModel.Brand
			var warehouse warehouseModel.Warehouse

			err := rows.Scan(
				// Inventory fields
				&inventory.ID, &inventory.WarehouseID, &inventory.ProductID,
				&inventory.BatchCode, &inventory.ExpirationDate, &inventory.CurrentStock,
				&inventory.ReservedStock, &inventory.AvailableStock, &inventory.MinStock,
				&inventory.MaxStock, &inventory.ReorderPoint, &inventory.LastMovementDate,
				&inventory.CreatedAt, &inventory.UpdatedAt, &inventory.DeletedAt,
				// Product fields
				&product.Name, &product.ImageURL, &product.CommercialName,
				&product.Code, &product.SKUCode, &product.MeasurementUnitID,
				&product.State, &product.Description, &product.CanBeSold,
				&product.CanBePurchased, &product.CostPrice, &product.CostPriceTotal,
				&product.CreatedAt, &product.UpdatedAt, &product.DeletedAt,
				// Brand fields
				&brand.ID, &brand.Name, &brand.Code,
				&brand.CreatedAt, &brand.UpdatedAt, &brand.DeletedAt,
				// Warehouse fields
				&warehouse.Name, &warehouse.Code, &warehouse.Type,
				&warehouse.Category, &warehouse.Description, &warehouse.Address,
				&warehouse.IsActive, &warehouse.IsSystemWarehouse,
				&warehouse.CreatedAt, &warehouse.UpdatedAt, &warehouse.DeletedAt,
				// Category IDs
				&product.CategoryIDs,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan inventory with details", err, nil)
			}

			// Set IDs for related entities
			product.ID = inventory.ProductID
			product.BrandID = brand.ID
			warehouse.ID = inventory.WarehouseID

			inventory.Product = product
			inventory.Brand = brand
			inventory.Warehouse = warehouse

			inventories = append(inventories, inventory)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return inventories, nil
}
