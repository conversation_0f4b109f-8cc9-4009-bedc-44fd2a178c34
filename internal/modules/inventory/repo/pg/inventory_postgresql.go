package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type InventoryPostgreRepo interface {
	Create(ctx context.Context, inventory model.Inventory) error
	Update(ctx context.Context, inventory model.Inventory) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Inventory, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.InventoryWithDetails, error)
	GetAllWithDetails(ctx context.Context) ([]model.InventoryWithDetails, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.InventoryWithDetails, error)
	GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.InventoryWithDetails, error)
	GetByProductID(ctx context.Context, productID string) ([]model.InventoryWithDetails, error)
	GetByBatchCode(ctx context.Context, batchCode string) ([]model.InventoryWithDetails, error)
	GetLowStockItems(ctx context.Context) ([]model.InventoryWithDetails, error)
	GetExpiringItems(ctx context.Context, days int) ([]model.InventoryWithDetails, error)
	Delete(ctx context.Context, id string) error
	CountByWarehouseProductBatch(ctx context.Context, warehouseID, productID, batchCode string) (int, error)
}

type inventoryPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewInventoryPostgreRepo(pool *pgxpool.Pool) InventoryPostgreRepo {
	return &inventoryPostgreRepo{
		pool: pool,
	}
}
