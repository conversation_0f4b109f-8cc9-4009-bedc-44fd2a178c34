package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (i *inventoryPostgreRepo) Update(ctx context.Context, inventory model.Inventory) error {
	return pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE inventory SET
				warehouse_id = $2,
				product_id = $3,
				batch_code = $4,
				expiration_date = $5,
				current_stock = $6,
				reserved_stock = $7,
				available_stock = $8,
				min_stock = $9,
				max_stock = $10,
				reorder_point = $11,
				last_movement_date = $12,
				updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			inventory.ID,
			inventory.WarehouseID,
			inventory.ProductID,
			inventory.BatchCode,
			inventory.ExpirationDate,
			inventory.CurrentStock,
			inventory.ReservedStock,
			inventory.AvailableStock,
			inventory.MinStock,
			inventory.MaxStock,
			inventory.ReorderPoint,
			inventory.LastMovementDate,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update inventory", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.InventoryNotFoundf("Inventory not found", nil, nil)
		}

		return nil
	})
}
