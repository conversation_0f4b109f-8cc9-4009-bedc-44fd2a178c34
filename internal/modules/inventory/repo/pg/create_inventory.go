package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (i *inventoryPostgreRepo) Create(ctx context.Context, inventory model.Inventory) error {
	return pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO inventory (
				id, warehouse_id, product_id, batch_code, expiration_date,
				current_stock, reserved_stock, available_stock, min_stock,
				max_stock, reorder_point, last_movement_date
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		`

		_, err := conn.Exec(ctx, query,
			inventory.ID,
			inventory.WarehouseID,
			inventory.ProductID,
			inventory.BatchCode,
			inventory.ExpirationDate,
			inventory.CurrentStock,
			inventory.ReservedStock,
			inventory.AvailableStock,
			inventory.MinStock,
			inventory.MaxStock,
			inventory.ReorderPoint,
			inventory.LastMovementDate,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create inventory", err, nil)
		}

		return nil
	})
}
