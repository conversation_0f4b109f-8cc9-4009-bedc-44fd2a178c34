CREATE TABLE dev.inventory (
    id VARCHAR(255) PRIMARY KEY,
    warehouse_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    batch_code VARCHAR(255) NOT NULL,
    expiration_date DATE,
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    reserved_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    available_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    min_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    max_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    reorder_point DECIMAL(10,2) NOT NULL DEFAULT 0,
    last_movement_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (warehouse_id) REFERENCES dev.warehouses(id),
    FOREIGN KEY (product_id) REFERENCES dev.products(id),
    UNIQUE(warehouse_id, product_id, batch_code)
);

-- Index for better performance on common queries
CREATE INDEX idx_inventory_warehouse_id ON dev.inventory(warehouse_id);
CREATE INDEX idx_inventory_product_id ON dev.inventory(product_id);
CREATE INDEX idx_inventory_batch_code ON dev.inventory(batch_code);
CREATE INDEX idx_inventory_expiration_date ON dev.inventory(expiration_date);
CREATE INDEX idx_inventory_available_stock ON dev.inventory(available_stock);
CREATE INDEX idx_inventory_last_movement_date ON dev.inventory(last_movement_date);
