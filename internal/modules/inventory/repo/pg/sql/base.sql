-- Base query for inventory with product, category, and brand information
-- This query joins inventory with products, brands, categories, and warehouses
-- to return complete inventory information with related entity details

SELECT 
    i.id,
    i.warehouse_id,
    i.product_id,
    i.batch_code,
    i.expiration_date,
    i.current_stock,
    i.reserved_stock,
    i.available_stock,
    i.min_stock,
    i.max_stock,
    i.reorder_point,
    i.last_movement_date,
    i.created_at,
    i.updated_at,
    i.deleted_at,
    -- Product information
    p.name as product_name,
    p.image_url as product_image_url,
    p.commercial_name as product_commercial_name,
    p.code as product_code,
    p.sku_code as product_sku_code,
    p.measurement_unit_id as product_measurement_unit_id,
    p.state as product_state,
    p.description as product_description,
    p.can_be_sold as product_can_be_sold,
    p.can_be_purchased as product_can_be_purchased,
    p.cost_price as product_cost_price,
    p.cost_price_total as product_cost_price_total,
    -- Brand information
    b.id as brand_id,
    b.name as brand_name,
    b.code as brand_code,
    -- Warehouse information
    w.name as warehouse_name,
    w.code as warehouse_code,
    w.type as warehouse_type,
    w.category as warehouse_category,
    -- Category IDs array
    COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
FROM dev.inventory i
JOIN dev.products p ON i.product_id = p.id
JOIN dev.brands b ON p.brand_id = b.id
JOIN dev.warehouses w ON i.warehouse_id = w.id
LEFT JOIN dev.product_categories pc ON p.id = pc.product_id
WHERE i.deleted_at IS NULL 
  AND p.deleted_at IS NULL 
  AND b.deleted_at IS NULL 
  AND w.deleted_at IS NULL
GROUP BY 
    i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
    i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
    i.max_stock, i.reorder_point, i.last_movement_date, i.created_at,
    i.updated_at, i.deleted_at,
    p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
    p.measurement_unit_id, p.state, p.description, p.can_be_sold,
    p.can_be_purchased, p.cost_price, p.cost_price_total,
    b.id, b.name, b.code,
    w.name, w.code, w.type, w.category;
