package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (i *inventoryPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Inventory, error) {
	var inventory model.Inventory

	err := pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT 
				id, warehouse_id, product_id, batch_code, expiration_date,
				current_stock, reserved_stock, available_stock, min_stock,
				max_stock, reorder_point, last_movement_date,
				created_at, updated_at, deleted_at
			FROM inventory
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)

		err := row.Scan(
			&inventory.ID,
			&inventory.WarehouseID,
			&inventory.ProductID,
			&inventory.BatchCode,
			&inventory.ExpirationDate,
			&inventory.CurrentStock,
			&inventory.ReservedStock,
			&inventory.AvailableStock,
			&inventory.MinStock,
			&inventory.MaxStock,
			&inventory.ReorderPoint,
			&inventory.LastMovementDate,
			&inventory.CreatedAt,
			&inventory.UpdatedAt,
			&inventory.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.InventoryNotFoundf("Inventory not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get inventory by prop", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &inventory, nil
}

func (i *inventoryPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int

	err := pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM inventory
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count inventory by prop", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (i *inventoryPostgreRepo) CountByWarehouseProductBatch(ctx context.Context, warehouseID, productID, batchCode string) (int, error) {
	var count int

	err := pg.ExecuteInSchema(ctx, i.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT COUNT(*)
			FROM inventory
			WHERE warehouse_id = $1 AND product_id = $2 AND batch_code = $3 AND deleted_at IS NULL
		`

		row := conn.QueryRow(ctx, query, warehouseID, productID, batchCode)
		err := row.Scan(&count)
		if err != nil {
			return utils.InternalErrorf("failed to count inventory by warehouse, product, and batch", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}
