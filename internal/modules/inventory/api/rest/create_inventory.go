package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type inventoryCreate struct {
	WarehouseID      string     `json:"warehouse_id" validate:"required"`
	ProductID        string     `json:"product_id" validate:"required"`
	BatchCode        string     `json:"batch_code" validate:"required"`
	ExpirationDate   *time.Time `json:"expiration_date"`
	CurrentStock     float64    `json:"current_stock" validate:"min=0"`
	ReservedStock    float64    `json:"reserved_stock" validate:"min=0"`
	AvailableStock   float64    `json:"available_stock" validate:"min=0"`
	MinStock         float64    `json:"min_stock" validate:"min=0"`
	MaxStock         float64    `json:"max_stock" validate:"min=0"`
	ReorderPoint     float64    `json:"reorder_point" validate:"min=0"`
	LastMovementDate *time.Time `json:"last_movement_date"`
}

func inventoryCreateToModel(req inventoryCreate) model.InventoryCreate {
	return model.InventoryCreate{
		WarehouseID:      req.WarehouseID,
		ProductID:        req.ProductID,
		BatchCode:        req.BatchCode,
		ExpirationDate:   req.ExpirationDate,
		CurrentStock:     req.CurrentStock,
		ReservedStock:    req.ReservedStock,
		AvailableStock:   req.AvailableStock,
		MinStock:         req.MinStock,
		MaxStock:         req.MaxStock,
		ReorderPoint:     req.ReorderPoint,
		LastMovementDate: req.LastMovementDate,
	}
}

// Create implements InventoryHandler.
func (i *inventoryHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[inventoryCreate](w, r, i.validator)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		return
	}

	id, err := i.useCase.Create(ctx, inventoryCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to create inventory")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
