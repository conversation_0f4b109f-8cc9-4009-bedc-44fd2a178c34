package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements InventoryHandler.
func (i *inventoryHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if err := i.useCase.Delete(ctx, id); err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to delete inventory")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
