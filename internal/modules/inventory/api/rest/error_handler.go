package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.InventoryConflictCode:          http.StatusConflict,
	model.InventoryConflictBatchCodeCode: http.StatusConflict,
	model.InventoryNotFoundCode:          http.StatusNotFound,
	model.InventoryValidationErrorCode:   http.StatusBadRequest,
	model.InventoryInsufficientStockCode: http.StatusBadRequest,
	model.InventoryExpiredBatchCode:      http.StatusBadRequest,
	model.InventoryNegativeStockCode:     http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
