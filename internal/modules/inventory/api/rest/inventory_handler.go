package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type InventoryHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	GetByWarehouseID(w http.ResponseWriter, r *http.Request)
	GetByProductID(w http.ResponseWriter, r *http.Request)
	GetByBatchCode(w http.ResponseWriter, r *http.Request)
	GetLowStockItems(w http.ResponseWriter, r *http.Request)
	GetExpiringItems(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateBatchCode(w http.ResponseWriter, r *http.Request)
}

type inventoryHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.InventoryUsecase
}

func NewInventoryHandler(log *logrus.Logger, validator *validator.Validate, useCase model.InventoryUsecase) InventoryHandler {
	return &inventoryHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
