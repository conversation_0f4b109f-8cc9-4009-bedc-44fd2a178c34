package rest

import (
	"net/http"
	"strconv"
	"time"

	brandModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type inventoryResult struct {
	ID               string                    `json:"id"`
	WarehouseID      string                    `json:"warehouse_id"`
	ProductID        string                    `json:"product_id"`
	BatchCode        string                    `json:"batch_code"`
	ExpirationDate   *time.Time                `json:"expiration_date"`
	CurrentStock     float64                   `json:"current_stock"`
	ReservedStock    float64                   `json:"reserved_stock"`
	AvailableStock   float64                   `json:"available_stock"`
	MinStock         float64                   `json:"min_stock"`
	MaxStock         float64                   `json:"max_stock"`
	ReorderPoint     float64                   `json:"reorder_point"`
	LastMovementDate *time.Time                `json:"last_movement_date"`
	CreatedAt        *time.Time                `json:"created_at"`
	UpdatedAt        *time.Time                `json:"updated_at"`
	DeletedAt        *time.Time                `json:"deleted_at"`
	Product          productModel.Product      `json:"product"`
	Brand            brandModel.Brand          `json:"brand"`
	Warehouse        warehouseModel.Warehouse  `json:"warehouse"`
}

func inventoryWithDetailsToResult(inventory *model.InventoryWithDetails) inventoryResult {
	return inventoryResult{
		ID:               inventory.ID,
		WarehouseID:      inventory.WarehouseID,
		ProductID:        inventory.ProductID,
		BatchCode:        inventory.BatchCode,
		ExpirationDate:   inventory.ExpirationDate,
		CurrentStock:     inventory.CurrentStock,
		ReservedStock:    inventory.ReservedStock,
		AvailableStock:   inventory.AvailableStock,
		MinStock:         inventory.MinStock,
		MaxStock:         inventory.MaxStock,
		ReorderPoint:     inventory.ReorderPoint,
		LastMovementDate: inventory.LastMovementDate,
		CreatedAt:        inventory.CreatedAt,
		UpdatedAt:        inventory.UpdatedAt,
		DeletedAt:        inventory.DeletedAt,
		Product:          inventory.Product,
		Brand:            inventory.Brand,
		Warehouse:        inventory.Warehouse,
	}
}

// GetById implements InventoryHandler.
func (i *inventoryHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	inventory, err := i.useCase.GetByPropWithDetails(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get inventory")
		return
	}

	result := inventoryWithDetailsToResult(inventory)
	rest.SuccessDResponse(w, r, result, http.StatusOK)
}

// GetAll implements InventoryHandler.
func (i *inventoryHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	inventories, err := i.useCase.GetAllWithDetails(ctx)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get inventories")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByWarehouseID implements InventoryHandler.
func (i *inventoryHandler) GetByWarehouseID(w http.ResponseWriter, r *http.Request) {
	warehouseID := r.PathValue("warehouse_id")
	ctx := r.Context()

	inventories, err := i.useCase.GetByWarehouseID(ctx, warehouseID)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get inventories by warehouse")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByProductID implements InventoryHandler.
func (i *inventoryHandler) GetByProductID(w http.ResponseWriter, r *http.Request) {
	productID := r.PathValue("product_id")
	ctx := r.Context()

	inventories, err := i.useCase.GetByProductID(ctx, productID)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get inventories by product")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByBatchCode implements InventoryHandler.
func (i *inventoryHandler) GetByBatchCode(w http.ResponseWriter, r *http.Request) {
	batchCode := r.PathValue("batch_code")
	ctx := r.Context()

	inventories, err := i.useCase.GetByBatchCode(ctx, batchCode)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get inventories by batch code")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetLowStockItems implements InventoryHandler.
func (i *inventoryHandler) GetLowStockItems(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	inventories, err := i.useCase.GetLowStockItems(ctx)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get low stock items")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetExpiringItems implements InventoryHandler.
func (i *inventoryHandler) GetExpiringItems(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	// Get days parameter from query string, default to 30 days
	daysStr := r.URL.Query().Get("days")
	days := 30 // default
	if daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		}
	}

	inventories, err := i.useCase.GetExpiringItems(ctx, days)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Failed to get expiring items")
		return
	}

	results := make([]inventoryResult, len(inventories))
	for i, inventory := range inventories {
		results[i] = inventoryWithDetailsToResult(&inventory)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
