package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ValidateBatchCode implements InventoryHandler.
func (i *inventoryHandler) ValidateBatchCode(w http.ResponseWriter, r *http.Request) {
	warehouseID := r.PathValue("warehouse_id")
	productID := r.PathValue("product_id")
	batchCode := r.PathValue("batch_code")
	ctx := r.Context()

	if warehouseID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Warehouse ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	if productID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Product ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	if batchCode == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Batch code parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := i.useCase.ValidateBatchCode(ctx, warehouseID, productID, batchCode)
	if err != nil {
		utils.LogErr(ctx, i.log, err)
		respErrHandler(w, r, err, "Batch code validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
