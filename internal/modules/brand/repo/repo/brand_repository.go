package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/repo/pg"
)

type brandRepository struct {
	pgRepo pg.BrandPostgreRepo
}

// CountByProp implements model.BrandRepository.
func (b *brandRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return b.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.BrandRepository.
func (b *brandRepository) Create(ctx context.Context, brand model.Brand) error {
	return b.pgRepo.Create(ctx, brand)
}

// Delete implements model.BrandRepository.
func (b *brandRepository) Delete(ctx context.Context, id string) error {
	return b.pgRepo.Delete(ctx, id)
}

// GetAll implements model.BrandRepository.
func (b *brandRepository) GetAll(ctx context.Context) ([]model.Brand, error) {
	return b.pgRepo.GetAll(ctx)
}

// GetByProp implements model.BrandRepository.
func (b *brandRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Brand, error) {
	return b.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.BrandRepository.
func (b *brandRepository) Update(ctx context.Context, brand model.Brand) error {
	return b.pgRepo.Update(ctx, brand)
}

func NewBrandRepository(pgRepo pg.BrandPostgreRepo) model.BrandRepository {
	return &brandRepository{
		pgRepo: pgRepo,
	}
}
