package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *brandPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Brand, error) {
	var brand model.Brand
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"name": true,
			"code": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM brands
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&brand.ID,
			&brand.Name,
			&brand.Code,
			&brand.CreatedAt,
			&brand.UpdatedAt,
			&brand.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.BrandNotFoundf("brand not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get brand", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &brand, nil
}

func (b *brandPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"name": true,
			"code": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM brands
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count brands", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (b *brandPostgreRepo) GetAll(ctx context.Context) ([]model.Brand, error) {
	var brands []model.Brand
	err := pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM brands
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get brands", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var brand model.Brand
			err := rows.Scan(
				&brand.ID,
				&brand.Name,
				&brand.Code,
				&brand.CreatedAt,
				&brand.UpdatedAt,
				&brand.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan brand", err, nil)
			}
			brands = append(brands, brand)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return brands, nil
}
