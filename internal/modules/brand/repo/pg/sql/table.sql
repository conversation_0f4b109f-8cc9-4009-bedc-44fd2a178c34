CREATE TABLE dev.brands (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    code VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_brands_name ON dev.brands(name);
CREATE INDEX idx_brands_code ON dev.brands(code);
CREATE INDEX idx_brands_deleted_at ON dev.brands(deleted_at);
CREATE INDEX idx_brands_created_at ON dev.brands(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_brands_updated_at BEFORE UPDATE ON dev.brands
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
