package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type BrandPostgreRepo interface {
	Create(ctx context.Context, brand model.Brand) error
	Update(ctx context.Context, brand model.Brand) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Brand, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Brand, error)
	Delete(ctx context.Context, id string) error
}

type brandPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewBrandPostgreRepo(pool *pgxpool.Pool) BrandPostgreRepo {
	return &brandPostgreRepo{
		pool: pool,
	}
}
