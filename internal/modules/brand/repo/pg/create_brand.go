package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *brandPostgreRepo) Create(ctx context.Context, brand model.Brand) error {
	return pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO brands (id, name, code)
			VALUES ($1, $2, $3)
		`

		_, err := conn.Exec(ctx, query,
			brand.ID,
			brand.Name,
			brand.Code,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create brand", err, nil)
		}

		return nil
	})
}
