package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *brandPostgreRepo) Update(ctx context.Context, brand model.Brand) error {
	return pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE brands
			SET name = $2, code = $3
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			brand.ID,
			brand.Name,
			brand.Code,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update brand", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.BrandNotFoundf("brand not found", nil, nil)
		}

		return nil
	})
}
