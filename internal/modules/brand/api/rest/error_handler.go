package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.BrandConflictCode:     http.StatusConflict,
	model.BrandConflictNameCode: http.StatusConflict,
	model.BrandConflictCodeCode: http.StatusConflict,
	model.BrandNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
