package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type BrandHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
}

type brandHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.BrandUsecase
}

func NewBrandHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.BrandUsecase,
) BrandHandler {
	return &brandHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
