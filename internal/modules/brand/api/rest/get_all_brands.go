package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetAll implements BrandHandler.
func (b *brandHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	brands, err := b.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get all brands")
		return
	}

	results := make([]brandResult, len(brands))
	for i, brand := range brands {
		results[i] = brandToResult(&brand)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
