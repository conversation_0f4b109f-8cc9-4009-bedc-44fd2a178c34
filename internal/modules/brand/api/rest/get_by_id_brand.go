package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements BrandHandler.
func (b *brandHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	brand, err := b.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to get brand by id")
		return
	}

	rest.SuccessDResponse(w, r, brandToResult(brand), http.StatusOK)
}
