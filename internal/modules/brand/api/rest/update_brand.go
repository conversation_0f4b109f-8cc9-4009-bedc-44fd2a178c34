package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements BrandHandler.
func (b *brandHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[model.BrandUpdate](w, r, b.validator)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		return
	}

	if err := b.useCase.Update(ctx, *req); err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to update brand")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
