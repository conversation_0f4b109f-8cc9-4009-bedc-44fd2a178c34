package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
)

type brandResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Code      string     `json:"code"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func brandToResult(brand *model.Brand) brandResult {
	return brandResult{
		ID:        brand.ID,
		Name:      brand.Name,
		Code:      brand.Code,
		CreatedAt: brand.CreatedAt,
		UpdatedAt: brand.UpdatedAt,
		DeletedAt: brand.DeletedAt,
	}
}

type brandCreate struct {
	Name string `json:"name" validate:"required"`
	Code string `json:"code" validate:"required"`
}

func brandCreateToModel(dto brandCreate) model.BrandCreate {
	return model.BrandCreate{
		Name: dto.Name,
		Code: dto.Code,
	}
}
