package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	BrandConflictCode utils.ErrCode = utils.BrandCode + iota
	BrandConflictNameCode
	BrandConflictCodeCode
	BrandNotFoundCode
)

func BrandConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(BrandConflictCode, message, err, details)
}

func BrandConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(BrandConflictNameCode, message, err, details)
}

func BrandConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(BrandConflictCodeCode, message, err, details)
}

func BrandNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(BrandNotFoundCode, message, err, details)
}
