package model

import "context"

type BrandRepository interface {
	Create(ctx context.Context, brand Brand) error
	Update(ctx context.Context, brand Brand) error
	GetByProp(ctx context.Context, prop string, value string) (*Brand, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Brand, error)
	Delete(ctx context.Context, id string) error
}
