package model

import "context"

type BrandUsecase interface {
	Create(ctx context.Context, brand BrandCreate) (string, error)
	Update(ctx context.Context, brand BrandUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Brand, error)
	GetAll(ctx context.Context) ([]Brand, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
}
