package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.BrandUsecase.
func (b *brandUsecase) Create(ctx context.Context, brand model.BrandCreate) (string, error) {
	// Check if name already exists
	nameExists, err := b.repo.CountByProp(ctx, "name", brand.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if brand name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.BrandConflictNamef("Brand name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := b.repo.CountByProp(ctx, "code", brand.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if brand code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.BrandConflictCodef("Brand code already exists", nil, nil)
	}

	newBrand := model.Brand{
		ID:   utils.UniqueId(),
		Name: brand.Name,
		Code: brand.Code,
	}

	err = b.repo.Create(ctx, newBrand)
	if err != nil {
		return "", err
	}

	return newBrand.ID, nil
}
