package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.BrandUsecase.
func (b *brandUsecase) Update(ctx context.Context, brand model.BrandUpdate) error {
	// Get the current brand to check if name/code has changed
	currentBrand, err := b.repo.GetByProp(ctx, "id", brand.ID)
	if err != nil {
		return err
	}

	// Check if name has changed and if it's already in use
	if currentBrand.Name != brand.Name {
		nameExists, err := b.repo.CountByProp(ctx, "name", brand.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if brand name exists", err, nil)
		}

		if nameExists > 0 {
			return model.BrandConflictNamef("Brand name already exists", nil, nil)
		}
	}

	// Check if code has changed and if it's already in use
	if currentBrand.Code != brand.Code {
		codeExists, err := b.repo.CountByProp(ctx, "code", brand.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if brand code exists", err, nil)
		}

		if codeExists > 0 {
			return model.BrandConflictCodef("Brand code already exists", nil, nil)
		}
	}

	updatedBrand := model.Brand{
		ID:   brand.ID,
		Name: brand.Name,
		Code: brand.Code,
	}

	err = b.repo.Update(ctx, updatedBrand)
	if err != nil {
		return err
	}

	return nil
}
