package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.CategoryUsecase.
func (c *categoryUsecase) Create(ctx context.Context, category model.CategoryCreate) (string, error) {
	// Check if code already exists
	codeExists, err := c.repo.CountByProp(ctx, "code", category.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if category code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.CategoryConflictCodef("Category code already exists", nil, nil)
	}

	// Check if name already exists
	nameExists, err := c.repo.CountByProp(ctx, "name", category.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if category name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.CategoryConflictNamef("Category name already exists", nil, nil)
	}

	// Validate parent category exists if provided
	if category.CategoryID != nil && *category.CategoryID != "" {
		_, err := c.repo.GetByProp(ctx, "id", *category.CategoryID)
		if err != nil {
			return "", model.CategoryInvalidParentf("Parent category does not exist", err, nil)
		}
	}

	newCategory := model.Category{
		ID:         utils.UniqueId(),
		Code:       category.Code,
		Name:       category.Name,
		CategoryID: category.CategoryID,
	}

	err = c.repo.Create(ctx, newCategory)
	if err != nil {
		return "", err
	}

	return newCategory.ID, nil
}
