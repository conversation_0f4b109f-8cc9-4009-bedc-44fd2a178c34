package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
)

// GetCategoryWithDetails implements model.CategoryUsecase.
func (c *categoryUsecase) GetCategoryWithDetails(ctx context.Context, categoryID string) (*model.CategoryWithSubcategories, error) {
	// Get the main category
	category, err := c.repo.GetByProp(ctx, "id", categoryID)
	if err != nil {
		return nil, err
	}

	// Get subcategories
	subcategories, err := c.repo.GetSubcategories(ctx, categoryID)
	if err != nil {
		return nil, err
	}

	// Get parent category
	parent, err := c.repo.GetParentCategory(ctx, categoryID)
	if err != nil {
		return nil, err
	}

	return &model.CategoryWithSubcategories{
		Category:      *category,
		Subcategories: subcategories,
		Parent:        parent,
	}, nil
}
