package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.CategoryUsecase.
func (c *categoryUsecase) Update(ctx context.Context, category model.CategoryUpdate) error {
	// Get the current category to check if code/name has changed
	currentCategory, err := c.repo.GetByProp(ctx, "id", category.ID)
	if err != nil {
		return err
	}

	// Check if code has changed and if it's already in use
	if currentCategory.Code != category.Code {
		codeExists, err := c.repo.CountByProp(ctx, "code", category.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if category code exists", err, nil)
		}

		if codeExists > 0 {
			return model.CategoryConflictCodef("Category code already exists", nil, nil)
		}
	}

	// Check if name has changed and if it's already in use
	if currentCategory.Name != category.Name {
		nameExists, err := c.repo.CountByProp(ctx, "name", category.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if category name exists", err, nil)
		}

		if nameExists > 0 {
			return model.CategoryConflictNamef("Category name already exists", nil, nil)
		}
	}

	// Validate parent category exists if provided and changed
	if category.CategoryID != nil && *category.CategoryID != "" {
		// Prevent self-referencing
		if *category.CategoryID == category.ID {
			return model.CategoryInvalidParentf("Category cannot be its own parent", nil, nil)
		}

		_, err := c.repo.GetByProp(ctx, "id", *category.CategoryID)
		if err != nil {
			return model.CategoryInvalidParentf("Parent category does not exist", err, nil)
		}
	}

	updatedCategory := model.Category{
		ID:         category.ID,
		Code:       category.Code,
		Name:       category.Name,
		CategoryID: category.CategoryID,
	}

	err = c.repo.Update(ctx, updatedCategory)
	if err != nil {
		return err
	}

	return nil
}
