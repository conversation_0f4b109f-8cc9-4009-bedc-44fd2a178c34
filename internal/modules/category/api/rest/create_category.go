package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements CategoryHandler.
func (c *categoryHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[categoryCreate](w, r, c.validator)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		return
	}

	id, err := c.useCase.Create(ctx, categoryCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to create category")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
