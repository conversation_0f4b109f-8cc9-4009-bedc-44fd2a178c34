package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.CategoryConflictCode:       http.StatusConflict,
	model.CategoryConflictNameCode:   http.StatusConflict,
	model.CategoryConflictCodeCode:   http.StatusConflict,
	model.CategoryNotFoundCode:       http.StatusNotFound,
	model.CategoryInvalidParentCode:  http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
