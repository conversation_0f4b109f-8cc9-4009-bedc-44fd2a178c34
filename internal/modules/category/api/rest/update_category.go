package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type categoryUpdate struct {
	ID         string  `json:"id" validate:"required"`
	Code       string  `json:"code" validate:"required"`
	Name       string  `json:"name" validate:"required"`
	CategoryID *string `json:"category_id"`
}

func categoryUpdateToModel(dto categoryUpdate) model.CategoryUpdate {
	return model.CategoryUpdate{
		ID:         dto.ID,
		Code:       dto.Code,
		Name:       dto.Name,
		CategoryID: dto.CategoryID,
	}
}

// Update implements CategoryHandler.
func (c *categoryHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[categoryUpdate](w, r, c.validator)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		return
	}

	if err := c.useCase.Update(ctx, categoryUpdateToModel(*req)); err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to update category")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
