package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
)

type categoryResult struct {
	ID         string     `json:"id"`
	Code       string     `json:"code"`
	Name       string     `json:"name"`
	CategoryID *string    `json:"category_id"`
	CreatedAt  *time.Time `json:"created_at"`
	UpdatedAt  *time.Time `json:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at"`
}

func categoryToResult(category *model.Category) categoryResult {
	return categoryResult{
		ID:         category.ID,
		Code:       category.Code,
		Name:       category.Name,
		CategoryID: category.CategoryID,
		CreatedAt:  category.CreatedAt,
		UpdatedAt:  category.UpdatedAt,
		DeletedAt:  category.DeletedAt,
	}
}

type categoryCreate struct {
	Code       string  `json:"code" validate:"required"`
	Name       string  `json:"name" validate:"required"`
	CategoryID *string `json:"category_id"`
}

func categoryCreateToModel(dto categoryCreate) model.CategoryCreate {
	return model.CategoryCreate{
		Code:       dto.Code,
		Name:       dto.Name,
		CategoryID: dto.CategoryID,
	}
}

type categoryWithDetailsResult struct {
	Category      categoryResult   `json:"category"`
	Subcategories []categoryResult `json:"subcategories"`
	Parent        *categoryResult  `json:"parent"`
}

func categoryWithDetailsToResult(details *model.CategoryWithSubcategories) categoryWithDetailsResult {
	result := categoryWithDetailsResult{
		Category:      categoryToResult(&details.Category),
		Subcategories: make([]categoryResult, len(details.Subcategories)),
	}

	for i, sub := range details.Subcategories {
		result.Subcategories[i] = categoryToResult(&sub)
	}

	if details.Parent != nil {
		parent := categoryToResult(details.Parent)
		result.Parent = &parent
	}

	return result
}
