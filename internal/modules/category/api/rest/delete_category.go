package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements CategoryHandler.
func (c *categoryHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	err := c.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to delete category")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
