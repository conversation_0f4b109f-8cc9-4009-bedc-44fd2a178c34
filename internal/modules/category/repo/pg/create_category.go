package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *categoryPostgreRepo) Create(ctx context.Context, category model.Category) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO categories (id, code, name, category_id)
			VALUES ($1, $2, $3, $4)
		`

		_, err := conn.Exec(ctx, query,
			category.ID,
			category.Code,
			category.Name,
			category.CategoryID,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create category", err, nil)
		}

		return nil
	})
}
