package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *categoryPostgreRepo) Update(ctx context.Context, category model.Category) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE categories
			SET code = $2, name = $3, category_id = $4
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			category.ID,
			category.Code,
			category.Name,
			category.CategoryID,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update category", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.CategoryNotFoundf("category not found", nil, nil)
		}

		return nil
	})
}
