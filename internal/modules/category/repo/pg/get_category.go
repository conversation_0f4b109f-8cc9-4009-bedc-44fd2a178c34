package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *categoryPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Category, error) {
	var category model.Category
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, code, name, category_id, created_at, updated_at, deleted_at
			FROM categories
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&category.ID,
			&category.Code,
			&category.Name,
			&category.CategoryID,
			&category.CreatedAt,
			&category.UpdatedAt,
			&category.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.CategoryNotFoundf("category not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get category", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &category, nil
}

func (c *categoryPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM categories
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count categories", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (c *categoryPostgreRepo) GetAll(ctx context.Context) ([]model.Category, error) {
	var categories []model.Category
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, name, category_id, created_at, updated_at, deleted_at
			FROM categories
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get categories", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var category model.Category
			err := rows.Scan(
				&category.ID,
				&category.Code,
				&category.Name,
				&category.CategoryID,
				&category.CreatedAt,
				&category.UpdatedAt,
				&category.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan category", err, nil)
			}
			categories = append(categories, category)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return categories, nil
}

func (c *categoryPostgreRepo) GetSubcategories(ctx context.Context, categoryID string) ([]model.Category, error) {
	var categories []model.Category
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, name, category_id, created_at, updated_at, deleted_at
			FROM categories
			WHERE category_id = $1 AND deleted_at IS NULL
			ORDER BY name ASC
		`

		rows, err := conn.Query(ctx, query, categoryID)
		if err != nil {
			return utils.InternalErrorf("failed to get subcategories", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var category model.Category
			err := rows.Scan(
				&category.ID,
				&category.Code,
				&category.Name,
				&category.CategoryID,
				&category.CreatedAt,
				&category.UpdatedAt,
				&category.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan subcategory", err, nil)
			}
			categories = append(categories, category)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return categories, nil
}

func (c *categoryPostgreRepo) GetParentCategories(ctx context.Context) ([]model.Category, error) {
	var categories []model.Category
	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, name, category_id, created_at, updated_at, deleted_at
			FROM categories
			WHERE category_id IS NULL AND deleted_at IS NULL
			ORDER BY name ASC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get parent categories", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var category model.Category
			err := rows.Scan(
				&category.ID,
				&category.Code,
				&category.Name,
				&category.CategoryID,
				&category.CreatedAt,
				&category.UpdatedAt,
				&category.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan parent category", err, nil)
			}
			categories = append(categories, category)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return categories, nil
}

func (c *categoryPostgreRepo) GetParentCategory(ctx context.Context, categoryID string) (*model.Category, error) {
	// First get the category to find its parent ID
	category, err := c.GetByProp(ctx, "id", categoryID)
	if err != nil {
		return nil, err
	}

	if category.CategoryID == nil {
		return nil, nil // No parent category
	}

	// Get the parent category
	parentCategory, err := c.GetByProp(ctx, "id", *category.CategoryID)
	if err != nil {
		return nil, err
	}

	return parentCategory, nil
}
