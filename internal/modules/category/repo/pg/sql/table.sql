CREATE TABLE dev.categories (
    id VARCHAR(255) PRIMARY KEY,
    code VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    category_id VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (category_id) REFERENCES dev.categories(id)
);

CREATE INDEX idx_categories_code ON dev.categories(code);
CREATE INDEX idx_categories_name ON dev.categories(name);
CREATE INDEX idx_categories_category_id ON dev.categories(category_id);
CREATE INDEX idx_categories_deleted_at ON dev.categories(deleted_at);
CREATE INDEX idx_categories_created_at ON dev.categories(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON dev.categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
