package model

import "time"

type Category struct {
	ID         string
	Code       string
	Name       string
	CategoryID *string // Self-referencing foreign key (nullable)
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type CategoryCreate struct {
	Code       string
	Name       string
	CategoryID *string
}

type CategoryUpdate struct {
	ID         string
	Code       string
	Name       string
	CategoryID *string
}

type CategoryWithSubcategories struct {
	Category      Category
	Subcategories []Category
	Parent        *Category
}
