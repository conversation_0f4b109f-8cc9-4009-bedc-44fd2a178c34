package model

import "context"

type CategoryRepository interface {
	Create(ctx context.Context, category Category) error
	Update(ctx context.Context, category Category) error
	GetByProp(ctx context.Context, prop string, value string) (*Category, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Category, error)
	Delete(ctx context.Context, id string) error
	GetSubcategories(ctx context.Context, categoryID string) ([]Category, error)
	GetParentCategories(ctx context.Context) ([]Category, error)
	GetParentCategory(ctx context.Context, categoryID string) (*Category, error)
}
