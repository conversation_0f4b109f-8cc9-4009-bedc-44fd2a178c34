package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type validateAbbreviationRequest struct {
	Abbreviation string `json:"abbreviation" validate:"required"`
}

// ValidateAbbreviation implements MeasurementUnitHandler.
func (m *measurementUnitHandler) ValidateAbbreviation(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[validateAbbreviationRequest](w, r, m.validator)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		return
	}

	if err := m.useCase.ValidateAbbreviation(ctx, req.Abbreviation); err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Abbreviation validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
