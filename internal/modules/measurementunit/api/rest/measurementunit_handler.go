package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type MeasurementUnitHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	ValidateAbbreviation(w http.ResponseWriter, r *http.Request)
}

type measurementUnitHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.MeasurementUnitUsecase
}

func NewMeasurementUnitHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.MeasurementUnitUsecase,
) MeasurementUnitHandler {
	return &measurementUnitHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
