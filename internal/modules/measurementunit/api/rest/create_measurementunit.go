package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements MeasurementUnitHandler.
func (m *measurementUnitHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[measurementUnitCreate](w, r, m.validator)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		return
	}

	id, err := m.useCase.Create(ctx, measurementUnitCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Failed to create measurement unit")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
