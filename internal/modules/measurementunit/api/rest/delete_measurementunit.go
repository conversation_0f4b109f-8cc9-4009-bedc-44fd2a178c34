package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements MeasurementUnitHandler.
func (m *measurementUnitHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := m.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Failed to delete measurement unit")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
