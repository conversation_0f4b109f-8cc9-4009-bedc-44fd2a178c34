package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements MeasurementUnitHandler.
func (m *measurementUnitHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	measurementUnit, err := m.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Failed to get measurement unit")
		return
	}

	result := measurementUnitToResult(measurementUnit)
	rest.SuccessDResponse(w, r, result, http.StatusOK)
}

// GetAll implements MeasurementUnitHandler.
func (m *measurementUnitHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	measurementUnits, err := m.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Failed to get measurement units")
		return
	}

	var results []measurementUnitResult
	for _, measurementUnit := range measurementUnits {
		results = append(results, measurementUnitToResult(&measurementUnit))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
