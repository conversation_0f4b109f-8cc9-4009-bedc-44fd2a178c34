package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements UnitMeasurementCategoryHandler.
func (u *unitMeasurementCategoryHandler) Delete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	if err := u.useCase.Delete(ctx, id); err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to delete unit measurement category")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
