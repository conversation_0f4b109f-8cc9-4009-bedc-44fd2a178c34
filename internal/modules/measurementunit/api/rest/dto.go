package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
)

// Unit Measurement Category DTOs
type unitMeasurementCategoryResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Code      string     `json:"code"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func unitMeasurementCategoryToResult(category *model.UnitMeasurementCategory) unitMeasurementCategoryResult {
	return unitMeasurementCategoryResult{
		ID:        category.ID,
		Name:      category.Name,
		Code:      category.Code,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
		DeletedAt: category.DeletedAt,
	}
}

type unitMeasurementCategoryCreate struct {
	Name string `json:"name" validate:"required"`
	Code string `json:"code" validate:"required"`
}

func unitMeasurementCategoryCreateToModel(dto unitMeasurementCategoryCreate) model.UnitMeasurementCategoryCreate {
	return model.UnitMeasurementCategoryCreate{
		Name: dto.Name,
		Code: dto.Code,
	}
}

type unitMeasurementCategoryUpdate struct {
	ID   string `json:"id" validate:"required"`
	Name string `json:"name" validate:"required"`
	Code string `json:"code" validate:"required"`
}

func unitMeasurementCategoryUpdateToModel(dto unitMeasurementCategoryUpdate) model.UnitMeasurementCategoryUpdate {
	return model.UnitMeasurementCategoryUpdate{
		ID:   dto.ID,
		Name: dto.Name,
		Code: dto.Code,
	}
}

// Measurement Unit DTOs
type measurementUnitResult struct {
	ID                        string     `json:"id"`
	Name                      string     `json:"name"`
	Code                      string     `json:"code"`
	UnitMeasurementCategoryID *string    `json:"unit_measurement_category_id"`
	Abbreviation              *string    `json:"abbreviation"`
	Type                      *string    `json:"type"`
	ConversionFactor          *float64   `json:"conversion_factor"`
	State                     *string    `json:"state"`
	CreatedAt                 *time.Time `json:"created_at"`
	UpdatedAt                 *time.Time `json:"updated_at"`
	DeletedAt                 *time.Time `json:"deleted_at"`
}

func measurementUnitToResult(measurementUnit *model.MeasurementUnit) measurementUnitResult {
	return measurementUnitResult{
		ID:                        measurementUnit.ID,
		Name:                      measurementUnit.Name,
		Code:                      measurementUnit.Code,
		UnitMeasurementCategoryID: measurementUnit.UnitMeasurementCategoryID,
		Abbreviation:              measurementUnit.Abbreviation,
		Type:                      measurementUnit.Type,
		ConversionFactor:          measurementUnit.ConversionFactor,
		State:                     measurementUnit.State,
		CreatedAt:                 measurementUnit.CreatedAt,
		UpdatedAt:                 measurementUnit.UpdatedAt,
		DeletedAt:                 measurementUnit.DeletedAt,
	}
}

type measurementUnitCreate struct {
	Name                      string   `json:"name" validate:"required"`
	Code                      string   `json:"code" validate:"required"`
	UnitMeasurementCategoryID *string  `json:"unit_measurement_category_id"`
	Abbreviation              *string  `json:"abbreviation"`
	Type                      *string  `json:"type" validate:"omitempty,oneof=less base greater"`
	ConversionFactor          *float64 `json:"conversion_factor"`
	State                     *string  `json:"state"`
}

func measurementUnitCreateToModel(dto measurementUnitCreate) model.MeasurementUnitCreate {
	return model.MeasurementUnitCreate{
		Name:                      dto.Name,
		Code:                      dto.Code,
		UnitMeasurementCategoryID: dto.UnitMeasurementCategoryID,
		Abbreviation:              dto.Abbreviation,
		Type:                      dto.Type,
		ConversionFactor:          dto.ConversionFactor,
		State:                     dto.State,
	}
}

type measurementUnitUpdate struct {
	ID                        string   `json:"id" validate:"required"`
	Name                      string   `json:"name" validate:"required"`
	Code                      string   `json:"code" validate:"required"`
	UnitMeasurementCategoryID *string  `json:"unit_measurement_category_id"`
	Abbreviation              *string  `json:"abbreviation"`
	Type                      *string  `json:"type" validate:"omitempty,oneof=less base greater"`
	ConversionFactor          *float64 `json:"conversion_factor"`
	State                     *string  `json:"state"`
}

func measurementUnitUpdateToModel(dto measurementUnitUpdate) model.MeasurementUnitUpdate {
	return model.MeasurementUnitUpdate{
		ID:                        dto.ID,
		Name:                      dto.Name,
		Code:                      dto.Code,
		UnitMeasurementCategoryID: dto.UnitMeasurementCategoryID,
		Abbreviation:              dto.Abbreviation,
		Type:                      dto.Type,
		ConversionFactor:          dto.ConversionFactor,
		State:                     dto.State,
	}
}
