package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type UnitMeasurementCategoryHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
}

type unitMeasurementCategoryHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.UnitMeasurementCategoryUsecase
}

func NewUnitMeasurementCategoryHandler(log *logrus.Logger, validator *validator.Validate, useCase model.UnitMeasurementCategoryUsecase) UnitMeasurementCategoryHandler {
	return &unitMeasurementCategoryHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
