package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements UnitMeasurementCategoryHandler.
func (u *unitMeasurementCategoryHandler) GetById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	id := r.PathValue("id")

	category, err := u.useCase.GetByProp(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to get unit measurement category")
		return
	}

	result := unitMeasurementCategoryToResult(category)
	rest.SuccessDResponse(w, r, result, http.StatusOK)
}

// GetAll implements UnitMeasurementCategoryHandler.
func (u *unitMeasurementCategoryHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	categories, err := u.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to get unit measurement categories")
		return
	}

	var results []unitMeasurementCategoryResult
	for _, category := range categories {
		results = append(results, unitMeasurementCategoryToResult(&category))
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
