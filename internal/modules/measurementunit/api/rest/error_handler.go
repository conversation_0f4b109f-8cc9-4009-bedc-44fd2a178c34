package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.MeasurementUnitConflictCode:     http.StatusConflict,
	model.MeasurementUnitConflictNameCode: http.StatusConflict,
	model.MeasurementUnitConflictCodeCode: http.StatusConflict,
	model.MeasurementUnitNotFoundCode:     http.StatusNotFound,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
