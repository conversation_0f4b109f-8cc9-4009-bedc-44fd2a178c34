package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type validateUnitMeasurementCategoryCodeRequest struct {
	Code string `json:"code" validate:"required"`
}

// ValidateCode implements UnitMeasurementCategoryHandler.
func (u *unitMeasurementCategoryHandler) ValidateCode(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[validateUnitMeasurementCategoryCodeRequest](w, r, u.validator)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		return
	}

	if err := u.useCase.ValidateCode(ctx, req.Code); err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Code validation failed")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
