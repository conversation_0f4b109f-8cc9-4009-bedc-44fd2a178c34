package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements UnitMeasurementCategoryHandler.
func (u *unitMeasurementCategoryHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[unitMeasurementCategoryUpdate](w, r, u.validator)
	if err != nil {
		utils.LogErr(ctx, u.log, err)
		return
	}

	if err := u.useCase.Update(ctx, unitMeasurementCategoryUpdateToModel(*req)); err != nil {
		utils.LogErr(ctx, u.log, err)
		respErrHandler(w, r, err, "Failed to update unit measurement category")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
