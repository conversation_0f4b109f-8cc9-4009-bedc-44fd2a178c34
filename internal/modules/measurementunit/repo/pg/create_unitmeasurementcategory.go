package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *unitMeasurementCategoryPostgreRepo) Create(ctx context.Context, category model.UnitMeasurementCategory) error {
	return pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO unit_measurement_categories (id, name, code)
			VALUES ($1, $2, $3)
		`

		_, err := conn.Exec(ctx, query,
			category.ID,
			category.Name,
			category.Code,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create unit measurement category", err, nil)
		}

		return nil
	})
}
