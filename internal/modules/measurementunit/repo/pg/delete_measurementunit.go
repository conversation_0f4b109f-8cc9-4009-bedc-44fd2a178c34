package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (m *measurementUnitPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE measurement_units
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete measurement unit", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.MeasurementUnitNotFoundf("measurement unit not found", nil, nil)
		}

		return nil
	})
}
