package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (u *unitMeasurementCategoryPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.UnitMeasurementCategory, error) {
	var category model.UnitMeasurementCategory
	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM unit_measurement_categories
			WHERE ` + prop + ` = $1 AND deleted_at IS NULL
		`

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&category.ID,
			&category.Name,
			&category.Code,
			&category.CreatedAt,
			&category.UpdatedAt,
			&category.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.UnitMeasurementCategoryNotFoundf("unit measurement category not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get unit measurement category", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &category, nil
}

func (u *unitMeasurementCategoryPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT COUNT(*)
			FROM unit_measurement_categories
			WHERE ` + prop + ` = $1 AND deleted_at IS NULL
		`

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count unit measurement categories", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (u *unitMeasurementCategoryPostgreRepo) GetAll(ctx context.Context) ([]model.UnitMeasurementCategory, error) {
	var categories []model.UnitMeasurementCategory
	err := pg.ExecuteInSchema(ctx, u.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM unit_measurement_categories
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get unit measurement categories", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var category model.UnitMeasurementCategory
			err := rows.Scan(
				&category.ID,
				&category.Name,
				&category.Code,
				&category.CreatedAt,
				&category.UpdatedAt,
				&category.DeletedAt,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan unit measurement category", err, nil)
			}

			categories = append(categories, category)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate unit measurement categories", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return categories, nil
}
