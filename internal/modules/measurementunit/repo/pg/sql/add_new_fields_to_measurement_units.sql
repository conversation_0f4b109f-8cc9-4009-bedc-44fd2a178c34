-- Migration script to add new fields to measurement_units table
-- This script adds unit_measurement_category_id, abbreviation, type, conversion_factor, and state fields

-- Step 1: Add unit_measurement_category_id column with foreign key
ALTER TABLE dev.measurement_units 
ADD COLUMN IF NOT EXISTS unit_measurement_category_id VARCHAR(255);

-- Step 2: Add abbreviation column (unique)
ALTER TABLE dev.measurement_units 
ADD COLUMN IF NOT EXISTS abbreviation VARCHAR(50);

-- Step 3: Add type column (can be 'less', 'base', or 'greater')
ALTER TABLE dev.measurement_units 
ADD COLUMN IF NOT EXISTS type VARCHAR(20);

-- Step 4: Add conversion_factor column
ALTER TABLE dev.measurement_units 
ADD COLUMN IF NOT EXISTS conversion_factor DECIMAL(15,6);

-- Step 5: Add state column
ALTER TABLE dev.measurement_units 
ADD COLUMN IF NOT EXISTS state VARCHAR(50) DEFAULT 'active';

-- Step 6: Add foreign key constraint to unit_measurement_categories table
-- Note: This assumes unit_measurement_categories table already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_measurement_units_category_id' 
        AND table_schema = 'dev' 
        AND table_name = 'measurement_units'
    ) THEN
        ALTER TABLE dev.measurement_units 
        ADD CONSTRAINT fk_measurement_units_category_id 
        FOREIGN KEY (unit_measurement_category_id) REFERENCES dev.unit_measurement_categories(id);
    END IF;
END $$;

-- Step 7: Add unique constraint for abbreviation (only when not null)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'unique_measurement_units_abbreviation' 
        AND table_schema = 'dev' 
        AND table_name = 'measurement_units'
    ) THEN
        ALTER TABLE dev.measurement_units 
        ADD CONSTRAINT unique_measurement_units_abbreviation 
        UNIQUE (abbreviation);
    END IF;
END $$;

-- Step 8: Add check constraint for type field
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'check_measurement_units_type' 
        AND constraint_schema = 'dev'
    ) THEN
        ALTER TABLE dev.measurement_units 
        ADD CONSTRAINT check_measurement_units_type 
        CHECK (type IS NULL OR type IN ('less', 'base', 'greater'));
    END IF;
END $$;

-- Step 9: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_measurement_units_category_id ON dev.measurement_units(unit_measurement_category_id);
CREATE INDEX IF NOT EXISTS idx_measurement_units_abbreviation ON dev.measurement_units(abbreviation);
CREATE INDEX IF NOT EXISTS idx_measurement_units_type ON dev.measurement_units(type);
CREATE INDEX IF NOT EXISTS idx_measurement_units_state ON dev.measurement_units(state);

-- Step 10: Add comments to document the new columns
COMMENT ON COLUMN dev.measurement_units.unit_measurement_category_id IS 'Foreign key reference to unit_measurement_categories table';
COMMENT ON COLUMN dev.measurement_units.abbreviation IS 'Short abbreviation for the measurement unit (e.g., kg, lb, m)';
COMMENT ON COLUMN dev.measurement_units.type IS 'Type of unit: less (smaller), base (standard), greater (larger)';
COMMENT ON COLUMN dev.measurement_units.conversion_factor IS 'Factor to convert to base unit';
COMMENT ON COLUMN dev.measurement_units.state IS 'Current state of the measurement unit (active, inactive, etc.)';
