CREATE TABLE dev.unit_measurement_categories (
    id VARCHAR(255) PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
    code VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for better performance
CREATE INDEX idx_unit_measurement_categories_name ON dev.unit_measurement_categories(name);
CREATE INDEX idx_unit_measurement_categories_code ON dev.unit_measurement_categories(code);
CREATE INDEX idx_unit_measurement_categories_deleted_at ON dev.unit_measurement_categories(deleted_at);
CREATE INDEX idx_unit_measurement_categories_created_at ON dev.unit_measurement_categories(created_at);

-- Add comments to document the table purpose
COMMENT ON TABLE dev.unit_measurement_categories IS 'Categories for grouping measurement units (e.g., Weight, Volume, Length)';
COMMENT ON COLUMN dev.unit_measurement_categories.id IS 'Unique identifier for the unit measurement category';
COMMENT ON COLUMN dev.unit_measurement_categories.name IS 'Human-readable name of the category';
COMMENT ON COLUMN dev.unit_measurement_categories.code IS 'Unique code for the category';
