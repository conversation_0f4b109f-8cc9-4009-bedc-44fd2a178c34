CREATE TABLE dev.measurement_units (
    id VARCHAR(255) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    code VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_measurement_units_name ON dev.measurement_units(name);
CREATE INDEX idx_measurement_units_code ON dev.measurement_units(code);
CREATE INDEX idx_measurement_units_deleted_at ON dev.measurement_units(deleted_at);
CREATE INDEX idx_measurement_units_created_at ON dev.measurement_units(created_at);

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_measurement_units_updated_at BEFORE UPDATE ON dev.measurement_units
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
