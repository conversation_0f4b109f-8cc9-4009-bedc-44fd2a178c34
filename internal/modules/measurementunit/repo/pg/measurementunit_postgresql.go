package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type MeasurementUnitPostgreRepo interface {
	Create(ctx context.Context, measurementUnit model.MeasurementUnit) error
	Update(ctx context.Context, measurementUnit model.MeasurementUnit) error
	GetByProp(ctx context.Context, prop string, value string) (*model.MeasurementUnit, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.MeasurementUnit, error)
	Delete(ctx context.Context, id string) error
}

type UnitMeasurementCategoryPostgreRepo interface {
	Create(ctx context.Context, category model.UnitMeasurementCategory) error
	Update(ctx context.Context, category model.UnitMeasurementCategory) error
	GetByProp(ctx context.Context, prop string, value string) (*model.UnitMeasurementCategory, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.UnitMeasurementCategory, error)
	Delete(ctx context.Context, id string) error
}

type measurementUnitPostgreRepo struct {
	pool *pgxpool.Pool
}

type unitMeasurementCategoryPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewMeasurementUnitPostgreRepo(pool *pgxpool.Pool) MeasurementUnitPostgreRepo {
	return &measurementUnitPostgreRepo{
		pool: pool,
	}
}

func NewUnitMeasurementCategoryPostgreRepo(pool *pgxpool.Pool) UnitMeasurementCategoryPostgreRepo {
	return &unitMeasurementCategoryPostgreRepo{
		pool: pool,
	}
}
