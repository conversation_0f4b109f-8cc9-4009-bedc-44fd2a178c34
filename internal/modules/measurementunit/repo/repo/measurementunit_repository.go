package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/repo/pg"
)

type measurementUnitRepository struct {
	pgRepo pg.MeasurementUnitPostgreRepo
}

type unitMeasurementCategoryRepository struct {
	pgRepo pg.UnitMeasurementCategoryPostgreRepo
}

// Measurement Unit Repository Implementation
// CountByProp implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return m.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Create(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return m.pgRepo.Create(ctx, measurementUnit)
}

// Delete implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Delete(ctx context.Context, id string) error {
	return m.pgRepo.Delete(ctx, id)
}

// GetAll implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) GetAll(ctx context.Context) ([]model.MeasurementUnit, error) {
	return m.pgRepo.GetAll(ctx)
}

// GetByProp implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) GetByProp(ctx context.Context, prop string, value string) (*model.MeasurementUnit, error) {
	return m.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Update(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return m.pgRepo.Update(ctx, measurementUnit)
}

// Unit Measurement Category Repository Implementation
// CountByProp implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return u.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) Create(ctx context.Context, category model.UnitMeasurementCategory) error {
	return u.pgRepo.Create(ctx, category)
}

// Delete implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) Delete(ctx context.Context, id string) error {
	return u.pgRepo.Delete(ctx, id)
}

// GetAll implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) GetAll(ctx context.Context) ([]model.UnitMeasurementCategory, error) {
	return u.pgRepo.GetAll(ctx)
}

// GetByProp implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) GetByProp(ctx context.Context, prop string, value string) (*model.UnitMeasurementCategory, error) {
	return u.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.UnitMeasurementCategoryRepository.
func (u *unitMeasurementCategoryRepository) Update(ctx context.Context, category model.UnitMeasurementCategory) error {
	return u.pgRepo.Update(ctx, category)
}

// Constructor functions
func NewMeasurementUnitRepository(pgRepo pg.MeasurementUnitPostgreRepo) model.MeasurementUnitRepository {
	return &measurementUnitRepository{
		pgRepo: pgRepo,
	}
}

func NewUnitMeasurementCategoryRepository(pgRepo pg.UnitMeasurementCategoryPostgreRepo) model.UnitMeasurementCategoryRepository {
	return &unitMeasurementCategoryRepository{
		pgRepo: pgRepo,
	}
}
