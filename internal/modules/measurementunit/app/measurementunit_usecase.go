package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
)

type measurementUnitUsecase struct {
	repo model.MeasurementUnitRepository
}

// Delete implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) Delete(ctx context.Context, id string) error {
	return m.repo.Delete(ctx, id)
}

// GetAll implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) GetAll(ctx context.Context) ([]model.MeasurementUnit, error) {
	return m.repo.GetAll(ctx)
}

// GetByProp implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.MeasurementUnit, error) {
	return m.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := m.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.MeasurementUnitConflictCodef("Measurement unit code already exists", nil, nil)
	}

	return nil
}

// ValidateAbbreviation implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) ValidateAbbreviation(ctx context.Context, abbreviation string) error {
	count, err := m.repo.CountByProp(ctx, "abbreviation", abbreviation)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.MeasurementUnitConflictAbbreviationf("Measurement unit abbreviation already exists", nil, nil)
	}

	return nil
}

func NewMeasurementUnitUsecase(repo model.MeasurementUnitRepository) model.MeasurementUnitUsecase {
	return &measurementUnitUsecase{
		repo: repo,
	}
}
