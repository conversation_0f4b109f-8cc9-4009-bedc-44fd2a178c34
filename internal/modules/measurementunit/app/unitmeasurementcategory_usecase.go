package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
)

type unitMeasurementCategoryUsecase struct {
	repo model.UnitMeasurementCategoryRepository
}

// Delete implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) Delete(ctx context.Context, id string) error {
	return u.repo.Delete(ctx, id)
}

// GetAll implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) GetAll(ctx context.Context) ([]model.UnitMeasurementCategory, error) {
	return u.repo.GetAll(ctx)
}

// GetByProp implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.UnitMeasurementCategory, error) {
	return u.repo.GetByProp(ctx, prop, value)
}

// ValidateCode implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) ValidateCode(ctx context.Context, code string) error {
	count, err := u.repo.CountByProp(ctx, "code", code)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.UnitMeasurementCategoryConflictCodef("Unit measurement category code already exists", nil, nil)
	}

	return nil
}

func NewUnitMeasurementCategoryUsecase(repo model.UnitMeasurementCategoryRepository) model.UnitMeasurementCategoryUsecase {
	return &unitMeasurementCategoryUsecase{
		repo: repo,
	}
}
