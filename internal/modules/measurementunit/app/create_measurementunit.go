package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) Create(ctx context.Context, measurementUnit model.MeasurementUnitCreate) (string, error) {
	// Check if name already exists
	nameExists, err := m.repo.CountByProp(ctx, "name", measurementUnit.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if measurement unit name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.MeasurementUnitConflictNamef("Measurement unit name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := m.repo.CountByProp(ctx, "code", measurementUnit.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if measurement unit code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.MeasurementUnitConflictCodef("Measurement unit code already exists", nil, nil)
	}

	// Check if abbreviation already exists (if provided)
	if measurementUnit.Abbreviation != nil && *measurementUnit.Abbreviation != "" {
		abbreviationExists, err := m.repo.CountByProp(ctx, "abbreviation", *measurementUnit.Abbreviation)
		if err != nil {
			return "", utils.InternalErrorf("Failed to check if measurement unit abbreviation exists", err, nil)
		}

		if abbreviationExists > 0 {
			return "", model.MeasurementUnitConflictAbbreviationf("Measurement unit abbreviation already exists", nil, nil)
		}
	}

	// Generate ID and create measurement unit
	id := ulid.Make().String()
	measurementUnitEntity := model.MeasurementUnit{
		ID:                        id,
		Name:                      measurementUnit.Name,
		Code:                      measurementUnit.Code,
		UnitMeasurementCategoryID: measurementUnit.UnitMeasurementCategoryID,
		Abbreviation:              measurementUnit.Abbreviation,
		Type:                      measurementUnit.Type,
		ConversionFactor:          measurementUnit.ConversionFactor,
		State:                     measurementUnit.State,
	}

	err = m.repo.Create(ctx, measurementUnitEntity)
	if err != nil {
		return "", err
	}

	return id, nil
}
