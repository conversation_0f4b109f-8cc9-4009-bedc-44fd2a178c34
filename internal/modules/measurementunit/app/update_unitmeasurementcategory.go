package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) Update(ctx context.Context, category model.UnitMeasurementCategoryUpdate) error {
	// Get the current unit measurement category to check if name/code has changed
	currentCategory, err := u.repo.GetByProp(ctx, "id", category.ID)
	if err != nil {
		return err
	}

	// Check if name has changed and if it's already in use
	if currentCategory.Name != category.Name {
		nameExists, err := u.repo.CountByProp(ctx, "name", category.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if unit measurement category name exists", err, nil)
		}

		if nameExists > 0 {
			return model.UnitMeasurementCategoryConflictNamef("Unit measurement category name already exists", nil, nil)
		}
	}

	// Check if code has changed and if it's already in use
	if currentCategory.Code != category.Code {
		codeExists, err := u.repo.CountByProp(ctx, "code", category.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if unit measurement category code exists", err, nil)
		}

		if codeExists > 0 {
			return model.UnitMeasurementCategoryConflictCodef("Unit measurement category code already exists", nil, nil)
		}
	}

	// Update the unit measurement category
	categoryEntity := model.UnitMeasurementCategory{
		ID:   category.ID,
		Name: category.Name,
		Code: category.Code,
	}

	return u.repo.Update(ctx, categoryEntity)
}
