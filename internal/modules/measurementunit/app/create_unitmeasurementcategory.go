package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.UnitMeasurementCategoryUsecase.
func (u *unitMeasurementCategoryUsecase) Create(ctx context.Context, category model.UnitMeasurementCategoryCreate) (string, error) {
	// Check if name already exists
	nameExists, err := u.repo.CountByProp(ctx, "name", category.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if unit measurement category name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.UnitMeasurementCategoryConflictNamef("Unit measurement category name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := u.repo.CountByProp(ctx, "code", category.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if unit measurement category code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.UnitMeasurementCategoryConflictCodef("Unit measurement category code already exists", nil, nil)
	}

	// Generate ID and create unit measurement category
	id := ulid.Make().String()
	categoryEntity := model.UnitMeasurementCategory{
		ID:   id,
		Name: category.Name,
		Code: category.Code,
	}

	err = u.repo.Create(ctx, categoryEntity)
	if err != nil {
		return "", err
	}

	return id, nil
}
