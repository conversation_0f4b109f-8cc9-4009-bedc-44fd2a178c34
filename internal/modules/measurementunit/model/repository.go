package model

import "context"

type MeasurementUnitRepository interface {
	Create(ctx context.Context, measurementUnit MeasurementUnit) error
	Update(ctx context.Context, measurementUnit MeasurementUnit) error
	GetByProp(ctx context.Context, prop string, value string) (*MeasurementUnit, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]MeasurementUnit, error)
	Delete(ctx context.Context, id string) error
}

type UnitMeasurementCategoryRepository interface {
	Create(ctx context.Context, category UnitMeasurementCategory) error
	Update(ctx context.Context, category UnitMeasurementCategory) error
	GetByProp(ctx context.Context, prop string, value string) (*UnitMeasurementCategory, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]UnitMeasurementCategory, error)
	Delete(ctx context.Context, id string) error
}
