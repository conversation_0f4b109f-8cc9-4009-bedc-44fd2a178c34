package model

import "context"

type MeasurementUnitUsecase interface {
	Create(ctx context.Context, measurementUnit MeasurementUnitCreate) (string, error)
	Update(ctx context.Context, measurementUnit MeasurementUnitUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*MeasurementUnit, error)
	GetAll(ctx context.Context) ([]MeasurementUnit, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
	ValidateAbbreviation(ctx context.Context, abbreviation string) error
}

type UnitMeasurementCategoryUsecase interface {
	Create(ctx context.Context, category UnitMeasurementCategoryCreate) (string, error)
	Update(ctx context.Context, category UnitMeasurementCategoryUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*UnitMeasurementCategory, error)
	GetAll(ctx context.Context) ([]UnitMeasurementCategory, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
}
