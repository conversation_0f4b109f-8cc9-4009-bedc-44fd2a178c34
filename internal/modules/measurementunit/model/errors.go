package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	// Measurement Unit Error Codes
	MeasurementUnitConflictCode utils.ErrCode = utils.MeasurementUnitCode + iota
	MeasurementUnitConflictNameCode
	MeasurementUnitConflictCodeCode
	MeasurementUnitConflictAbbreviationCode
	MeasurementUnitNotFoundCode

	// Unit Measurement Category Error Codes
	UnitMeasurementCategoryConflictCode
	UnitMeasurementCategoryConflictNameCode
	UnitMeasurementCategoryConflictCodeCode
	UnitMeasurementCategoryNotFoundCode
)

// Measurement Unit Error Functions
func MeasurementUnitConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictCode, message, err, details)
}

func MeasurementUnitConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictNameCode, message, err, details)
}

func MeasurementUnitConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictCodeCode, message, err, details)
}

func MeasurementUnitConflictAbbreviationf(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictAbbreviationCode, message, err, details)
}

func MeasurementUnitNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitNotFoundCode, message, err, details)
}

// Unit Measurement Category Error Functions
func UnitMeasurementCategoryConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(UnitMeasurementCategoryConflictCode, message, err, details)
}

func UnitMeasurementCategoryConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(UnitMeasurementCategoryConflictNameCode, message, err, details)
}

func UnitMeasurementCategoryConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(UnitMeasurementCategoryConflictCodeCode, message, err, details)
}

func UnitMeasurementCategoryNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(UnitMeasurementCategoryNotFoundCode, message, err, details)
}
