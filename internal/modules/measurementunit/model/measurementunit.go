package model

import "time"

type MeasurementUnit struct {
	ID                        string
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
	CreatedAt                 *time.Time
	UpdatedAt                 *time.Time
	DeletedAt                 *time.Time
}

type MeasurementUnitCreate struct {
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
}

type MeasurementUnitUpdate struct {
	ID                        string
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
}
