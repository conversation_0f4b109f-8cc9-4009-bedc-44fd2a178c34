meta {
  name: Update User
  type: http
  seq: 4
}

put {
  url: {{url}}/api/v1/users
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "id": "{{user_id}}",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "newpassword123"
  }
}

vars {
  user_id: replace-with-actual-user-id
}

tests {
  test("should return 204", function() {
    expect(res.getStatus()).to.equal(204);
  });
}
