meta {
  name: Get User By ID
  type: http
  seq: 3
}

get {
  url: {{url}}/api/v1/users
  body: none
  auth: bearer
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return user object", function() {
    const user = res.getBody();
    expect(user).to.have.property('id');
    expect(user).to.have.property('name');
    expect(user).to.have.property('email');
    expect(user).to.have.property('created_at');
  });
}
