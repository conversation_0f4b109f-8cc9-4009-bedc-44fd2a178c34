meta {
  name: Get All Users
  type: http
  seq: 2
}

get {
  url: {{url}}/api/v1/users
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of users", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("users should have required properties", function() {
    const users = res.getBody();
    if (users.length > 0) {
      expect(users[0]).to.have.property('id');
      expect(users[0]).to.have.property('name');
      expect(users[0]).to.have.property('email');
      expect(users[0]).to.have.property('created_at');
    }
  });
}
