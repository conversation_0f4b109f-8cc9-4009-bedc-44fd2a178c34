meta {
  name: Create User
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/users
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123"
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return user ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}
