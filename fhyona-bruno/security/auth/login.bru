meta {
  name: Login
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/auth/login
  body: json
  auth: none
}

body:json {
  {
    "username": "j<PERSON><PERSON>",
    "password": "1234"
  }
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return access token", function() {
    expect(res.getBody()).to.have.property('access_token');
  });
}
