# Fhyona API Bruno Collection

This Bruno collection contains API calls for testing the Fhyona v2 backend application.

## Complete Endpoint List

✅ **Authentication (3 endpoints):**
- POST /api/v1/auth/login
- POST /api/v1/auth/logout
- GET /api/v1/auth/is_logged_in

✅ **Users (5 endpoints):**
- POST /api/v1/users (Create)
- GET /api/v1/users (Get All)
- GET /api/v1/users/{id} (Get By ID)
- PUT /api/v1/users (Update)
- DELETE /api/v1/users/{id} (Delete)

✅ **Brands (5 endpoints):**
- POST /api/v1/brands (Create)
- GET /api/v1/brands (Get All)
- GET /api/v1/brands/{id} (Get By ID)
- PUT /api/v1/brands (Update)
- DELETE /api/v1/brands/{id} (Delete)

**Total: 13 endpoints with full CRUD operations**

## Setup

1. Install Bruno: https://www.usebruno.com/
2. Open the collection in Bruno by pointing to the `fhyona-bruno` folder
3. Select the `local` environment
4. If you don't see all endpoints, try refreshing the collection or restarting Bruno

## Environment Variables

- `url`: http://localhost:8000
- `access_token`: JWT token from login
- `user_id`: User ID for testing
- `brand_id`: Brand ID for testing

## Usage

1. Start your backend server on port 8000
2. Use Login endpoint to get access token
3. Set the token in environment variables
4. Test all CRUD operations for users and brands

## Database Setup

Run these SQL scripts:
- `internal/modules/user/repo/pg/sql/table.sql`
- `internal/modules/brand/repo/pg/sql/table.sql`