meta {
  name: Get Work Area By ID
  type: http
  seq: 3
}

get {
  url: {{url}}/api/v1/work-areas/{{work_area_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

vars {
  work_area_id: replace-with-actual-work-area-id
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return work area object", function() {
    const workArea = res.getBody();
    expect(workArea).to.have.property('id');
    expect(workArea).to.have.property('code');
    expect(workArea).to.have.property('name');
    expect(workArea).to.have.property('created_at');
  });
}
