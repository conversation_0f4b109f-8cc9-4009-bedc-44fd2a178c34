meta {
  name: Update Work Area
  type: http
  seq: 4
}

put {
  url: {{url}}/api/v1/work-areas
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "id": "{{work_area_id}}",
    "code": "ASSEMBLY_UPD",
    "name": "Assembly Line Updated"
  }
}

vars {
  work_area_id: replace-with-actual-work-area-id
}

tests {
  test("should return 204", function() {
    expect(res.getStatus()).to.equal(204);
  });
}
