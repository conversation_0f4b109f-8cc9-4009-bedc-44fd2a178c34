meta {
  name: Create Work Area
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/work-areas
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "code": "ASSEMBLY",
    "name": "Assembly Line"
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return work area ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}
