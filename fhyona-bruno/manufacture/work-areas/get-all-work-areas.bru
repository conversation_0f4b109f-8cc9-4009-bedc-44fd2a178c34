meta {
  name: Get All Work Areas
  type: http
  seq: 2
}

get {
  url: {{url}}/api/v1/work-areas
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of work areas", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("work areas should have required properties", function() {
    const workAreas = res.getBody();
    if (workAreas.length > 0) {
      expect(workAreas[0]).to.have.property('id');
      expect(workAreas[0]).to.have.property('code');
      expect(workAreas[0]).to.have.property('name');
      expect(workAreas[0]).to.have.property('created_at');
    }
  });
}
