meta {
  name: Get Recipe By ID
  type: http
  seq: 4
}

get {
  url: {{url}}/api/v1/recipes/01JFQR8XQZM8YBVN2K3P4Q5R6S
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return recipe object", function() {
    expect(res.getBody()).to.be.an('object');
    expect(res.getBody()).to.have.property('id');
    expect(res.getBody()).to.have.property('name');
    expect(res.getBody()).to.have.property('code');
    expect(res.getBody()).to.have.property('type');
    expect(res.getBody()).to.have.property('products');
    expect(res.getBody()).to.have.property('components');
  });
}
