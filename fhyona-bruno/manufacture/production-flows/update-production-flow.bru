meta {
  name: Update Production Flow
  type: http
  seq: 5
}

put {
  url: {{url}}/api/v1/production-flows
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "id": "{{production_flow_id}}",
    "code": "PROD_FLOW_001_UPDATED",
    "name": "Updated Production Flow",
    "product_id": "{{product_id}}"
  }
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
}
