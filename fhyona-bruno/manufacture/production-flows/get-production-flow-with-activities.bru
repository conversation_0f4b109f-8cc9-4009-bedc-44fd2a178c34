meta {
  name: Get Production Flow with Activities
  type: http
  seq: 4
}

get {
  url: {{url}}/api/v1/production-flows/{{production_flow_id}}/activities
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return production flow with activities", function() {
    const data = res.getBody();
    expect(data).to.be.an('object');
    expect(data).to.have.property('production_flow');
    expect(data).to.have.property('activities');
    expect(data.activities).to.be.an('array');
  });
  
  test("production flow should have required fields", function() {
    const flow = res.getBody().production_flow;
    expect(flow).to.have.property('id');
    expect(flow).to.have.property('code');
    expect(flow).to.have.property('name');
  });
  
  test("activities should have required fields", function() {
    const activities = res.getBody().activities;
    if (activities.length > 0) {
      expect(activities[0]).to.have.property('id');
      expect(activities[0]).to.have.property('index_number');
      expect(activities[0]).to.have.property('work_area');
      expect(activities[0]).to.have.property('operation');
      expect(activities[0].work_area).to.have.property('id');
      expect(activities[0].work_area).to.have.property('code');
      expect(activities[0].work_area).to.have.property('name');
      expect(activities[0].operation).to.have.property('id');
      expect(activities[0].operation).to.have.property('code');
      expect(activities[0].operation).to.have.property('name');
    }
  });
}
