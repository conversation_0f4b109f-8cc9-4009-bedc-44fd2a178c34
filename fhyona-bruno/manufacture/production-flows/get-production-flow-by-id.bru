meta {
  name: Get Production Flow by ID
  type: http
  seq: 3
}

get {
  url: {{url}}/api/v1/production-flows/01JXBXE1TDVB2M36A2Z784SQHX
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return production flow object", function() {
    const flow = res.getBody();
    expect(flow).to.be.an('object');
    expect(flow).to.have.property('id');
    expect(flow).to.have.property('code');
    expect(flow).to.have.property('name');
    expect(flow).to.have.property('created_at');
    expect(flow).to.have.property('updated_at');
  });
}
