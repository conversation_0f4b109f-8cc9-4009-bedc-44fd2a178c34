meta {
  name: Create Product without Production Info
  type: http
  seq: 7
}

post {
  url: {{base_url}}/products
  body: json
  auth: none
}

body:json {
  {
    "name": "Simple Product",
    "image_url": "https://example.com/simple.jpg",
    "commercial_name": "Simple Pro",
    "code": "SMP-001",
    "sku_code": "SKU-SMP-001",
    "measurement_unit_id": "{{measurement_unit_id}}",
    "category_ids": ["{{category_id}}"],
    "brand_id": "{{brand_id}}",
    "state": "active",
    "description": "A simple product that doesn't require production info",
    "can_be_sold": true,
    "can_be_purchased": true,
    "cost_price": 10.00,
    "cost_price_total": 1000
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Should return product ID", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.be.a('string');
  });
}
