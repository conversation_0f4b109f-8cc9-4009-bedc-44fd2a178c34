meta {
  name: Create Product with Production Info
  type: http
  seq: 6
}

post {
  url: {{base_url}}/products
  body: json
  auth: none
}

body:json {
  {
    "name": "Assembled Widget",
    "image_url": "https://example.com/widget.jpg",
    "commercial_name": "Widget Pro Assembly",
    "code": "WDG-ASM-001",
    "sku_code": "SKU-WDG-ASM-001",
    "measurement_unit_id": "{{measurement_unit_id}}",
    "category_ids": ["{{category_id}}"],
    "brand_id": "{{brand_id}}",
    "state": "active",
    "description": "A widget that requires assembly from multiple materials",
    "can_be_sold": true,
    "can_be_purchased": false,
    "cost_price": 25.50,
    "cost_price_total": 2550,
    "production_info": {
      "production_type": "assembly",
      "materials": [
        {
          "product_id": "{{material_product_id_1}}",
          "quantity": 2.5
        },
        {
          "product_id": "{{material_product_id_2}}",
          "quantity": 1.0
        }
      ]
    }
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Should return product ID", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.be.a('string');
  });
}
