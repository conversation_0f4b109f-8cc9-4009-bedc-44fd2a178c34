meta {
  name: Create Category
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/categories
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "code": "RAW-MATERIAL",
    "name": "Materias primas",
    "category_id": null
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return category ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}
