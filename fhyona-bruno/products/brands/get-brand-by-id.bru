meta {
  name: Get Brand By ID
  type: http
  seq: 3
}

get {
  url: {{url}}/api/v1/brands/01JX8RGMDVR6SJN18W9GAZDCW2
  body: none
  auth: bearer
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return brand object", function() {
    const brand = res.getBody();
    expect(brand).to.have.property('id');
    expect(brand).to.have.property('name');
    expect(brand).to.have.property('code');
    expect(brand).to.have.property('created_at');
  });
}
