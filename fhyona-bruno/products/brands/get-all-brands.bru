meta {
  name: Get All Brands
  type: http
  seq: 2
}

get {
  url: {{url}}/api/v1/brands
  body: none
  auth: bearer
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of brands", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("brands should have required properties", function() {
    const brands = res.getBody();
    if (brands.length > 0) {
      expect(brands[0]).to.have.property('id');
      expect(brands[0]).to.have.property('name');
      expect(brands[0]).to.have.property('code');
      expect(brands[0]).to.have.property('created_at');
    }
  });
}
