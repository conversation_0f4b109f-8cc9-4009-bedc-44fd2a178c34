meta {
  name: Validate Code
  type: http
  seq: 5
}

get {
  url: {{url}}/api/v1/brands/validate-code/NIKE_UPD
  body: none
  auth: bearer
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return brand object", function() {
    const brand = res.getBody();
    expect(brand).to.have.property('id');
    expect(brand).to.have.property('name');
    expect(brand).to.have.property('code');
    expect(brand).to.have.property('created_at');
  });
}
