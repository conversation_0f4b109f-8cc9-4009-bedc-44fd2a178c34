meta {
  name: Create Brand
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/brands
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "<PERSON>",
    "code": "NIKE"
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return brand ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}
