meta {
  name: Update Product
  type: http
  seq: 4
}

put {
  url: {{url}}/api/v1/products
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "id": "01JDQR8XQZM8YBVN9KPQR2ST3X",
    "name": "Laptop Dell Inspiron 15 Updated",
    "commercial_name": "Dell Inspiron 15 3000 Series",
    "code": "DELL-INS-15-UPD",
    "sku_code": "SKU-DELL-001-UPD",
    "measurement_unit_id": "01JDQR8XQZM8YBVN9KPQR2ST3U",
    "category_ids": ["01JDQR8XQZM8YBVN9KPQR2ST3V", "01JDQR8XQZM8YBVN9KPQR2ST3Y"],
    "brand_id": "01JDQR8XQZM8YBVN9KPQR2ST3W",
    "state": "active",
    "description": "Updated high-performance laptop for business and personal use",
    "can_be_sold": true,
    "can_be_purchased": true,
    "cost_price": 649.99,
    "image_url": "https://example.com/images/dell-inspiron-15-updated.jpg"
  }
}

tests {
  test("should return 204", function() {
    expect(res.getStatus()).to.equal(204);
  });
}
